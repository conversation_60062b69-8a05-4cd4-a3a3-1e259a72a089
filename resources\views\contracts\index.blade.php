<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('Contratos') }}
                </h2>
                <a href="{{ route('contracts.create') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                    <PERSON><PERSON><PERSON>
                </a>
            </div>
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Monto Total</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cuotas</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Estado</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($contracts as $contract)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $contract->id }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <a href="{{ route('clients.show', $contract->client) }}" class="text-indigo-600 hover:text-indigo-900">
                                                {{ $contract->client->nombre_completo }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="flex items-center">
                                                <span class="font-medium {{ $contract->estado_formateado['clase'] }} mr-2">
                                                    {{ $contract->estado_formateado['texto'] }}
                                                </span>
                                                @if($contract->estado_formateado['dias'])
                                                    <span class="text-sm text-gray-500">
                                                        ({{ $contract->estado_formateado['dias'] }})
                                                    </span>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            {{ $contract->cuotas_pagadas }} de {{ $contract->numero_cuotas }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            ${{ number_format($contract->monto_pendiente, 2) }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="{{ route('contracts.show', $contract) }}" 
                                               class="text-indigo-600 hover:text-indigo-900 mr-3">
                                                Ver
                                            </a>
                                            <a href="{{ route('contracts.edit', $contract) }}" 
                                               class="text-yellow-600 hover:text-yellow-900 mr-3">
                                                Editar
                                            </a>

                                            @if($contract->estado === 'activo')
                                                <a href="{{ route('payments.create') }}?contract_id={{ $contract->id }}" class="text-green-600 hover:text-green-900 mr-3">Pagar</a>
                                            @endif
                                            <button type="button" 
                                                    onclick="confirmDelete('{{ $contract->id }}')"
                                                    class="text-red-600 hover:text-red-900">
                                                Eliminar
                                            </button>
                                            <form id="delete-form-{{ $contract->id }}" 
                                                  action="{{ route('contracts.destroy', $contract) }}" 
                                                  method="POST" 
                                                  class="hidden">
                                                @csrf
                                                @method('DELETE')
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="6" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                                            No hay contratos registrados.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $contracts->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Verificar contratos por vencer y atrasados
            const contratos = {!! json_encode($contracts->map(function($contract) {
                return [
                    'id' => $contract->id,
                    'cliente' => $contract->client->nombre_completo,
                    'esta_por_vencer' => $contract->esta_por_vencer,
                    'esta_atrasado' => $contract->esta_atrasado,
                    'dias_atraso' => $contract->dias_atraso,
                    'dias_para_vencer' => $contract->dias_para_vencer
                ];
            })) !!};

            // Mostrar notificaciones para contratos por vencer
            const contratosPorVencer = contratos.filter(c => c.esta_por_vencer);
            if (contratosPorVencer.length > 0) {
                let mensajePorVencer = '<div class="text-left">';
                contratosPorVencer.forEach(c => {
                    mensajePorVencer += `
                        <div class="mb-2">
                            <strong>${c.cliente}</strong>: Vence en ${c.dias_para_vencer} días
                        </div>`;
                });
                mensajePorVencer += '</div>';

                Swal.fire({
                    title: 'Contratos Próximos a Vencer',
                    html: mensajePorVencer,
                    icon: 'warning',
                    confirmButtonText: 'Entendido'
                });
            }

            // Mostrar notificaciones para contratos atrasados
            const contratosAtrasados = contratos.filter(c => c.esta_atrasado);
            if (contratosAtrasados.length > 0) {
                setTimeout(() => {
                    let mensajeAtrasados = '<div class="text-left">';
                    contratosAtrasados.forEach(c => {
                        mensajeAtrasados += `
                            <div class="mb-2">
                                <strong>${c.cliente}</strong>: ${c.dias_atraso} días de atraso
                            </div>`;
                    });
                    mensajeAtrasados += '</div>';

                    Swal.fire({
                        title: 'Contratos Atrasados',
                        html: mensajeAtrasados,
                        icon: 'error',
                        confirmButtonText: 'Entendido'
                    });
                }, contratosPorVencer.length > 0 ? 3000 : 0);
            }
        });

        // Función para confirmar eliminación
        function confirmDelete(contractId) {
            Swal.fire({
                title: '¿Está seguro?',
                text: "Esta acción no se puede deshacer",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Sí, eliminar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById(`delete-form-${contractId}`).submit();
                }
            });
        }
    </script>
    @endpush
</x-app-layout> 