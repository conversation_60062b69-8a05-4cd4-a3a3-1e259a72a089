<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Lara<PERSON>') }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        :root {
            --sidebar-width: 280px;
            --sidebar-width-collapsed: 80px;
            --sidebar-background: 221.2 83.2% 53.3%;
            --sidebar-foreground: 210 40% 98%;
            --sidebar-primary: 0 0% 100%;
            --sidebar-primary-foreground: 221.2 83.2% 53.3%;
            --sidebar-accent: 217.2 91.2% 59.8% / 0.2;
            --sidebar-accent-foreground: 210 40% 98%;
            --sidebar-border: 214.3 84.6% 67.5% / 0.2;
            --sidebar-ring: 221.2 83.2% 53.3%;
        }

        .dark {
            --sidebar-background: 222.2 47.4% 11.2%;
            --sidebar-foreground: 210 40% 98%;
            --sidebar-primary: 210 40% 98%;
            --sidebar-primary-foreground: 222.2 47.4% 11.2%;
            --sidebar-accent: 217.2 32.6% 17.5%;
            --sidebar-accent-foreground: 210 40% 98%;
            --sidebar-border: 217.2 32.6% 17.5%;
            --sidebar-ring: 224.3 76.3% 48%;
        }

        body {
            font-family: 'Inter', sans-serif;
        }

        /* Sidebar Styles */
        .sidebar {
            background-color: hsl(var(--sidebar-background));
            color: hsl(var(--sidebar-foreground));
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            left: 0;
            top: 0;
            z-index: 50;
            transition: width 0.3s ease, transform 0.3s ease;
            display: flex;
            flex-direction: column;
        }

        .sidebar.collapsed {
            width: var(--sidebar-width-collapsed);
        }

        .sidebar-header {
            height: 64px;
            padding: 0 1.5rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid hsl(var(--sidebar-border));
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            overflow: hidden;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            background: hsl(var(--sidebar-accent));
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }

        .logo-text {
            font-weight: 600;
            font-size: 1.125rem;
            white-space: nowrap;
            transition: opacity 0.3s ease, width 0.3s ease;
        }

        .sidebar.collapsed .logo-text {
            opacity: 0;
            width: 0;
        }

        .toggle-sidebar {
            background: hsl(var(--sidebar-accent));
            border: none;
            color: hsl(var(--sidebar-foreground));
            width: 28px;
            height: 28px;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .toggle-sidebar:hover {
            background: hsl(var(--sidebar-accent-foreground) / 0.2);
        }

        .toggle-sidebar i {
            transition: transform 0.3s ease;
        }

        .sidebar.collapsed .toggle-sidebar i {
            transform: rotate(180deg);
        }

        /* Sidebar Content */
        .sidebar-content {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem 0;
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
        }

        .nav-section {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .nav-section-title {
            padding: 0 1.5rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            color: hsl(var(--sidebar-foreground) / 0.6);
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            transition: opacity 0.3s ease, height 0.3s ease;
        }

        .sidebar.collapsed .nav-section-title {
            opacity: 0;
            height: 0;
            margin: 0;
        }

        .nav-item {
            position: relative;
            margin: 0 0.75rem;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 0.625rem 0.75rem;
            border-radius: 0.5rem;
            color: hsl(var(--sidebar-foreground));
            transition: background-color 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .nav-link:hover {
            background-color: hsl(var(--sidebar-accent));
        }

        .nav-link.active {
            background-color: hsl(var(--sidebar-accent));
            font-weight: 500;
        }

        .nav-link.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 25%;
            height: 50%;
            width: 3px;
            background: hsl(var(--sidebar-primary));
            border-radius: 0 3px 3px 0;
        }

        .nav-icon {
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .nav-text {
            margin-left: 0.75rem;
            white-space: nowrap;
            transition: opacity 0.3s ease, width 0.3s ease;
        }

        .sidebar.collapsed .nav-text {
            opacity: 0;
            width: 0;
            margin-left: 0;
        }

        .nav-badge {
            margin-left: auto;
            background-color: hsl(var(--sidebar-primary));
            color: hsl(var(--sidebar-primary-foreground));
            font-size: 0.75rem;
            padding: 0.125rem 0.5rem;
            border-radius: 9999px;
            font-weight: 500;
            transition: opacity 0.3s ease, width 0.3s ease;
        }

        .sidebar.collapsed .nav-badge {
            opacity: 0;
            width: 0;
            margin: 0;
        }

        /* Tooltip for collapsed state */
        .nav-tooltip {
            position: absolute;
            left: calc(100% + 10px);
            top: 50%;
            transform: translateY(-50%);
            background: hsl(var(--sidebar-background));
            color: hsl(var(--sidebar-foreground));
            padding: 0.5rem 0.75rem;
            border-radius: 0.375rem;
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease, transform 0.2s ease;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            z-index: 60;
        }

        .sidebar.collapsed .nav-item:hover .nav-tooltip {
            opacity: 1;
            transform: translateY(-50%);
        }

        /* Sidebar Footer */
        .sidebar-footer {
            padding: 1rem;
            border-top: 1px solid hsl(var(--sidebar-border));
        }

        .help-card {
            background-color: hsl(var(--sidebar-accent));
            border-radius: 0.5rem;
            padding: 1rem;
            transition: padding 0.3s ease;
        }

        .sidebar.collapsed .help-card {
            padding: 0.75rem;
        }

        .help-title {
            font-weight: 600;
            font-size: 0.875rem;
            margin-bottom: 0.25rem;
            white-space: nowrap;
            overflow: hidden;
            transition: opacity 0.3s ease, height 0.3s ease;
        }

        .sidebar.collapsed .help-title {
            opacity: 0;
            height: 0;
            margin: 0;
        }

        .help-text {
            font-size: 0.75rem;
            color: hsl(var(--sidebar-foreground) / 0.7);
            margin-bottom: 0.75rem;
            white-space: nowrap;
            overflow: hidden;
            transition: opacity 0.3s ease, height 0.3s ease;
        }

        .sidebar.collapsed .help-text {
            opacity: 0;
            height: 0;
            margin: 0;
        }

        .help-button {
            background-color: hsl(var(--sidebar-primary));
            color: hsl(var(--sidebar-primary-foreground));
            border: none;
            border-radius: 0.375rem;
            padding: 0.5rem;
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: background-color 0.2s ease;
        }

        .help-button:hover {
            background-color: hsl(var(--sidebar-primary) / 0.9);
        }

        .sidebar.collapsed .help-button span {
            display: none;
        }

        /* User Profile Section */
        .user-profile {
            padding: 1rem 1.5rem;
            border-top: 1px solid hsl(var(--sidebar-border));
            display: flex;
            align-items: center;
            gap: 0.75rem;
            cursor: pointer;
            transition: background-color 0.2s ease;
        }

        .user-profile:hover {
            background-color: hsl(var(--sidebar-accent));
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: hsl(var(--sidebar-accent));
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            flex-shrink: 0;
        }

        .user-info {
            display: flex;
            flex-direction: column;
            overflow: hidden;
            transition: opacity 0.3s ease, width 0.3s ease;
        }

        .sidebar.collapsed .user-info {
            opacity: 0;
            width: 0;
        }

        .user-name {
            font-weight: 500;
            font-size: 0.875rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-role {
            font-size: 0.75rem;
            color: hsl(var(--sidebar-foreground) / 0.7);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .user-dropdown-icon {
            margin-left: auto;
            transition: opacity 0.3s ease, width 0.3s ease;
        }

        .sidebar.collapsed .user-dropdown-icon {
            opacity: 0;
            width: 0;
        }

        /* Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            transition: margin-left 0.3s ease;
            min-height: 100vh;
        }

        .sidebar.collapsed + .main-content {
            margin-left: var(--sidebar-width-collapsed);
        }

        /* Top Bar */
        .top-bar {
            height: 64px;
            background-color: white;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 1.5rem;
            position: sticky;
            top: 0;
            z-index: 40;
        }

        .dark .top-bar {
            background-color: #1e293b;
            border-color: #334155;
        }

        .mobile-menu-button {
            display: none;
            background: none;
            border: none;
            color: #6b7280;
            width: 40px;
            height: 40px;
            border-radius: 0.375rem;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .mobile-menu-button:hover {
            background-color: #f3f4f6;
            color: #111827;
        }

        .dark .mobile-menu-button:hover {
            background-color: #334155;
            color: #f9fafb;
        }

        .top-bar-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .theme-toggle {
            background: none;
            border: none;
            color: #6b7280;
            width: 40px;
            height: 40px;
            border-radius: 0.375rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        .theme-toggle:hover {
            background-color: #f3f4f6;
            color: #111827;
        }

        .dark .theme-toggle {
            color: #e5e7eb;
        }

        .dark .theme-toggle:hover {
            background-color: #334155;
            color: #f9fafb;
        }

        /* Mobile Overlay */
        .mobile-overlay {
            position: fixed;
            inset: 0;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 55;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.3s ease, visibility 0.3s ease;
        }

        .mobile-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        /* Responsive Styles */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
                z-index: 60;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0 !important;
            }

            .mobile-menu-button {
                display: flex;
            }

            .top-bar {
                padding: 0 1rem;
            }

            .logo-text {
                font-size: 1rem;
            }

            .nav-tooltip {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .top-bar {
                padding: 0 0.75rem;
            }
            
            .logo-text {
                display: none;
            }
            
            .sidebar-header {
                padding: 0 1rem;
            }

            .nav-link {
                padding: 0.875rem 0.75rem;
                min-height: 48px;
            }

            .mobile-menu-button, .theme-toggle {
                min-width: 44px;
                min-height: 44px;
            }
        }

        /* Touch improvements */
        @media (pointer: coarse) {
            .nav-link {
                min-height: 48px;
            }
            
            .mobile-menu-button, .theme-toggle {
                min-width: 44px;
                min-height: 44px;
            }
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div x-data="{ 
        sidebarCollapsed: localStorage.getItem('sidebarCollapsed') === 'true', 
        sidebarMobileOpen: false,
        darkMode: localStorage.getItem('darkMode') === 'true',
        toggleSidebar() {
            this.sidebarCollapsed = !this.sidebarCollapsed;
            localStorage.setItem('sidebarCollapsed', this.sidebarCollapsed);
        },
        toggleMobileSidebar() {
            this.sidebarMobileOpen = !this.sidebarMobileOpen;
        },
        closeMobileSidebar() {
            this.sidebarMobileOpen = false;
        },
        toggleDarkMode() {
            this.darkMode = !this.darkMode;
            localStorage.setItem('darkMode', this.darkMode);
            if (this.darkMode) {
                document.documentElement.classList.add('dark');
            } else {
                document.documentElement.classList.remove('dark');
            }
        },
        init() {
            if (this.darkMode) {
                document.documentElement.classList.add('dark');
            }
        }
    }" x-init="init()">
        <!-- Mobile Overlay -->
        <div class="mobile-overlay" :class="{'active': sidebarMobileOpen}" @click="toggleMobileSidebar"></div>
        
        <!-- Sidebar -->
        <aside class="sidebar" :class="{
            'collapsed': sidebarCollapsed,
            'active': sidebarMobileOpen
        }">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="logo-icon">S</div>
                    <div class="logo-text">Sistema de Pagos</div>
                </div>
                <button class="toggle-sidebar" @click="toggleSidebar">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <div class="sidebar-content">
                <!-- Principal Section -->
                <div class="nav-section">
                    <div class="nav-section-title">Principal</div>
                    
                    <div class="nav-item">
                        <a href="{{ route('dashboard') }}" class="nav-link {{ request()->routeIs('dashboard') ? 'active' : '' }}" @click="closeMobileSidebar()">
                            <div class="nav-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <span class="nav-text">Panel principal</span>
                            <div class="nav-tooltip">Panel principal</div>
                        </a>
                    </div>
                </div>

                <!-- Gestión Section -->
                <div class="nav-section">
                    <div class="nav-section-title">Gestión</div>
                    
                    <div class="nav-item">
                        <a href="{{ route('clients.index') }}" class="nav-link {{ request()->routeIs('clients.*') ? 'active' : '' }}" @click="closeMobileSidebar()">
                            <div class="nav-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <span class="nav-text">Clientes</span>
                            <div class="nav-tooltip">Clientes</div>
                        </a>
                    </div>
                    
                    <div class="nav-item">
                        <a href="{{ route('contracts.index') }}" class="nav-link {{ request()->routeIs('contracts.*') ? 'active' : '' }}" @click="closeMobileSidebar()">
                            <div class="nav-icon">
                                <i class="fas fa-file-contract"></i>
                            </div>
                            <span class="nav-text">Contratos</span>
                            <div class="nav-tooltip">Contratos</div>
                        </a>
                    </div>
                    
                    <div class="nav-item">
                        <a href="{{ route('payments.index') }}" class="nav-link {{ request()->routeIs('payments.*') ? 'active' : '' }}" @click="closeMobileSidebar()">
                            <div class="nav-icon">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <span class="nav-text">Pagos</span> 
                            <div class="nav-tooltip">Pagos</div>
                        </a>
                    </div>
                </div>

                <!-- Configuración Section -->
                <div class="nav-section">
                    <div class="nav-section-title">Configuración</div>
                    
                    <div class="nav-item">
                        <a href="{{ route('profile.edit') }}" class="nav-link {{ request()->routeIs('profile.*') ? 'active' : '' }}" @click="closeMobileSidebar()">
                            <div class="nav-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <span class="nav-text">Mi Perfil</span>
                            <div class="nav-tooltip">Mi Perfil</div>
                        </a>
                    </div>
                </div>
            </div>

            <!-- User Profile -->
            <div class="user-profile" x-data="{ userDropdownOpen: false }" @click.outside="userDropdownOpen = false">
                <div class="user-avatar">
                    {{ substr(Auth::user()->name ?? 'U', 0, 1) }}
                </div>
                <div class="user-info">
                    <div class="user-name">{{ Auth::user()->name ?? 'Usuario' }}</div>
                    <div class="user-role">Administrador</div>
                </div>
                <div class="user-dropdown-icon" @click.stop="userDropdownOpen = !userDropdownOpen">
                    <i class="fas fa-chevron-down"></i>
                </div>
                
                <!-- User Dropdown Menu -->
                <div x-show="userDropdownOpen" 
                     x-transition:enter="transition ease-out duration-100"
                     x-transition:enter-start="transform opacity-0 scale-95"
                     x-transition:enter-end="transform opacity-100 scale-100"
                     x-transition:leave="transition ease-in duration-75"
                     x-transition:leave-start="transform opacity-100 scale-100"
                     x-transition:leave-end="transform opacity-0 scale-95"
                     class="absolute bottom-full left-0 mb-2 w-56 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 focus:outline-none z-50"
                     style="display: none;">
                    <div class="py-1">
                        <a href="{{ route('profile.edit') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700" @click="closeMobileSidebar()">
                            <i class="fas fa-user mr-2"></i> Mi Perfil
                        </a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700" @click="closeMobileSidebar()">
                                <i class="fas fa-sign-out-alt mr-2"></i> Cerrar Sesión
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Help Section -->
            <div class="sidebar-footer">
                <div class="help-card">
                    <div class="help-title">¿Necesitas ayuda?</div>
                    <div class="help-text">Accede a nuestros recursos de soporte</div>
                    <button class="help-button">
                        <i class="fas fa-question-circle"></i>
                        <span>Centro de Ayuda</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Top Bar -->
            <div class="top-bar">
                <div class="flex items-center gap-3">
                    <button class="mobile-menu-button" @click="toggleMobileSidebar">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="text-lg font-semibold text-gray-900 dark:text-white">
                        Sistema de Pagos
                    </h1>
                </div>
                
                <div class="top-bar-actions">
                    <button class="theme-toggle" @click="toggleDarkMode">
                        <i class="fas fa-sun" x-show="darkMode"></i>
                        <i class="fas fa-moon" x-show="!darkMode"></i>
                    </button>
                </div>
            </div>
            
            <!-- Page Content -->
            <div class="p-4 sm:p-6">
                {{ $slot }}
            </div>
        </div>
    </div>
    <!-- Alpine.js -->
    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>
</body>
</html>