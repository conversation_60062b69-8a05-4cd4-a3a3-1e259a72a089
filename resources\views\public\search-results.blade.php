
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }} - Dashboard Ejecutivo | {{ $cedula }}</title>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        accent: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        luxury: {
                            50: '#fefce8',
                            100: '#fef9c3',
                            200: '#fef08a',
                            300: '#fde047',
                            400: '#facc15',
                            500: '#eab308',
                            600: '#ca8a04',
                            700: '#a16207',
                            800: '#854d0e',
                            900: '#713f12',
                        }
                    },
                    animation: {
                        'gradient': 'gradient 8s linear infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 3s infinite',
                    }
                }
            }
        }
    </script>    
<style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
            --luxury-gradient: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
            --platinum-gradient: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 50%, #ffffff 100%);
            --glass-bg: rgba(255, 255, 255, 0.02);
            --glass-border: rgba(255, 255, 255, 0.06);
            --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --card-bg: rgba(255, 255, 255, 0.04);
            --card-border: rgba(255, 255, 255, 0.08);
            --shadow-glow: 0 0 80px rgba(103, 126, 234, 0.2);
            --shadow-card: 0 30px 60px -12px rgba(0, 0, 0, 0.4);
            --shadow-input: 0 15px 35px -5px rgba(0, 0, 0, 0.2);
            --morphism-bg: rgba(255, 255, 255, 0.03);
            --morphism-border: rgba(255, 255, 255, 0.08);
            --morphism-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.3);
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.85);
            --text-muted: rgba(255, 255, 255, 0.65);
            --text-accent: #ffd700;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', system-ui, sans-serif;
            background: var(--primary-gradient);
            background-size: 400% 400%;
            animation: gradient 20s ease infinite;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            font-size: 12px;
            line-height: 1.4;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(ellipse at 20% 80%, rgba(102, 126, 234, 0.12) 0%, transparent 60%),
                radial-gradient(ellipse at 80% 20%, rgba(240, 147, 251, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 40% 40%, rgba(79, 172, 254, 0.06) 0%, transparent 60%),
                radial-gradient(ellipse at 60% 70%, rgba(255, 215, 0, 0.04) 0%, transparent 50%);
            z-index: -1;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.015'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(5deg); }
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: var(--primary-gradient);
            background-size: 400% 400%;
            animation: gradient 25s ease infinite;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 20s infinite ease-in-out;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation-delay: 5s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 15%;
            width: 60px;
            height: 60px;
            background: white;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation-delay: 10s;
        }

        .shape:nth-child(4) {
            bottom: 30%;
            right: 20%;
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 20px;
            animation-delay: 15s;
        }

        .glass-card {
            background: var(--card-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--card-border);
            box-shadow: var(--morphism-shadow);
            border-radius: 28px;
            position: relative;
            overflow: hidden;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--luxury-gradient);
            opacity: 0.6;
            border-radius: 28px 28px 0 0;
        }

        .glass-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, rgba(255, 215, 0, 0.3), transparent);
            border-radius: 28px 0 0 28px;
        }

        .morphism-button {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            border: 1px solid rgba(255, 255, 255, 0.15);
            box-shadow: 
                0 2px 8px 0 rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 8px;
            color: #ffffff;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            font-weight: 600;
            font-size: 0.6875rem;
            line-height: 1.2;
        }

        .morphism-button:hover {
            background: rgba(255, 215, 0, 0.1);
            transform: translateY(-2px) scale(1.02);
            box-shadow: 
                0 8px 20px 0 rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3),
                0 0 20px rgba(255, 215, 0, 0.2);
            border-color: rgba(255, 215, 0, 0.3);
            color: #ffffff;
        }

        .info-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 0.75rem;
            box-shadow: 
                0 2px 8px 0 rgba(0, 0, 0, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.4), transparent);
            border-radius: 16px 16px 0 0;
        }

        .info-card:hover {
            transform: translateY(-4px) scale(1.02);
            box-shadow: 
                0 8px 24px 0 rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 0 30px rgba(255, 215, 0, 0.15);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 215, 0, 0.2);
        }

        .status-badge {
            padding: 0.1875rem 0.5rem;
            border-radius: 6px;
            font-size: 0.625rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            line-height: 1.2;
        }

        .status-active {
            background: rgba(34, 197, 94, 0.15);
            color: #22c55e;
            border-color: rgba(34, 197, 94, 0.3);
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
        }

        .status-pending {
            background: rgba(245, 158, 11, 0.15);
            color: #ffffff;
            border-color: rgba(245, 158, 11, 0.3);
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
        }

        .status-overdue {
            background: rgba(239, 68, 68, 0.15);
            color: #ef4444;
            border-color: rgba(239, 68, 68, 0.3);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
        }

        .status-completado {
            background: rgba(34, 197, 94, 0.15);
            color: #22c55e;
            border-color: rgba(34, 197, 94, 0.3);
            box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);
        }

        .status-activo {
            background: rgba(59, 130, 246, 0.15);
            color: #3b82f6;
            border-color: rgba(59, 130, 246, 0.3);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .status-vencido {
            background: rgba(239, 68, 68, 0.15);
            color: #ef4444;
            border-color: rgba(239, 68, 68, 0.3);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);
        }

        .signature-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 1rem;
            box-shadow: 
                0 4px 12px 0 rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .signature-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 215, 0, 0.4), transparent);
            border-radius: 16px 16px 0 0;
        }

        .signature-section:hover {
            transform: translateY(-2px);
            box-shadow: 
                0 8px 24px 0 rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 0 30px rgba(255, 215, 0, 0.1);
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255, 215, 0, 0.2);
        }

        .signature-button-signed {
            background: rgba(34, 197, 94, 0.1);
            border-color: rgba(34, 197, 94, 0.3);
            color: #ffffff;
        }

        .signature-button-signed:hover {
            background: rgba(34, 197, 94, 0.15);
            border-color: rgba(34, 197, 94, 0.4);
            box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
            color: #ffffff;
        }

        .signature-button-pending {
            background: rgba(59, 130, 246, 0.1);
            border-color: rgba(59, 130, 246, 0.3);
            color: #ffffff;
        }

        .signature-button-pending:hover {
            background: rgba(59, 130, 246, 0.15);
            border-color: rgba(59, 130, 246, 0.4);
            box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
            color: #ffffff;
        }

        .signature-actions {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            flex-wrap: wrap;
        }

        .signature-actions .morphism-button {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .signature-actions .morphism-button:hover {
            transform: translateY(-2px) scale(1.05);
        }

        @media (max-width: 640px) {
            .signature-actions {
                flex-direction: column;
                width: 100%;
            }
            
            .signature-actions .morphism-button {
                width: 100%;
                justify-content: center;
            }
        }

        .progress-bar {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            height: 8px;
            overflow: hidden;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .progress-fill {
            background: var(--luxury-gradient);
            height: 100%;
            border-radius: 8px;
            transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 
                0 0 15px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .table-glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            overflow: hidden;
        }

        .table-glass th {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: 600;
            padding: 0.5rem;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table-glass td {
            padding: 0.5rem;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .table-glass tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* Floating particles effect */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float-particle 15s infinite linear;
        }

        @keyframes float-particle {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 2s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 4s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 6s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 8s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 10s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 12s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 14s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 16s; }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .stagger-children > * {
            animation: fadeInUp 0.6s ease-out backwards;
        }

        .stagger-children > *:nth-child(1) { animation-delay: 0.1s; }
        .stagger-children > *:nth-child(2) { animation-delay: 0.2s; }
        .stagger-children > *:nth-child(3) { animation-delay: 0.3s; }
        .stagger-children > *:nth-child(4) { animation-delay: 0.4s; }

        @media (max-width: 768px) {
            .glass-card {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>

<body class="min-h-screen antialiased">
    <!-- Animated Background -->
    <div class="animated-bg"></div>
    
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Floating Particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <!-- Header -->
    <header class="py-2">
        <div class="max-w-7xl mx-auto px-3">
            <div class="glass-card p-3">
                <div class="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-2">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600 rounded-xl flex items-center justify-center border border-white/20 shadow-lg">
                            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center space-x-2 mb-0.5">
                                <h1 class="text-sm font-bold text-white font-display">
                                    Dashboard Ejecutivo
                                </h1>
                                <div class="px-1.5 py-0.5 bg-green-500/20 border border-green-500/30 rounded-full text-green-300 text-xs font-medium">
                                    En Línea
                                </div>
                            </div>
                            <p class="text-white text-xs flex items-center">
                                <div class="w-3 h-3 bg-white/10 rounded flex items-center justify-center mr-1.5">
                                    <svg class="w-2 h-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                    </svg>
                                </div>
                                <span class="text-white">Cédula:</span> <span class="font-bold ml-1 text-yellow-300">{{ $cedula }}</span>
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-center gap-2">
                        <div class="flex items-center space-x-1 text-white/60 text-xs">
                            <div class="w-1 h-1 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-xs">Online</span>
                        </div>
                        
                        <div class="flex items-center space-x-1.5">
                            
                            <a href="{{ route('public.search') }}" class="morphism-button group text-xs">
                                <svg class="w-2.5 h-2.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                                </svg>
                                <span>Nueva</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="py-1">
        <div class="max-w-7xl mx-auto px-3 space-y-3">
            
            <!-- Client Information -->
            <section class="glass-card p-3 animate-fade-up">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <div class="w-7 h-7 bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-md">
                            <svg class="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                        <div>
                            <div class="flex items-center space-x-1.5 mb-0.5">
                                <h2 class="text-sm font-bold text-white font-display">Perfil del Cliente</h2>
                                <div class="px-1.5 py-0.5 bg-blue-500/20 border border-blue-500/30 rounded-full text-blue-300 text-xs font-medium">
                                    Verificado
                                </div>
                            </div>
                            <p class="text-white/70 text-xs">Información personal registrada</p>
                        </div>
                    </div>
                    
                    <div class="text-right">
                        <div class="text-xs text-white/60 mb-0.5">ID Cliente</div>
                        <div class="text-sm font-bold text-white">#{{ $client->id }}</div>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2 stagger-children">
                    <!-- Nombre -->
                    <div class="info-card group">
                        <div class="flex items-center space-x-1.5">
                            <div class="w-6 h-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shadow-sm transition-transform group-hover:scale-110">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs text-white/60 uppercase tracking-wide font-medium">Nombre</p>
                                <p class="text-xs font-bold text-white leading-tight">{{ $client->nombre_completo }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Cédula -->
                    <div class="info-card group">
                        <div class="flex items-center space-x-1.5">
                            <div class="w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-sm transition-transform group-hover:scale-110">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs text-white/60 uppercase tracking-wide font-medium">Cédula</p>
                                <p class="text-xs font-bold text-white">{{ $client->cedula }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Teléfono -->
                    <div class="info-card group">
                        <div class="flex items-center space-x-1.5">
                            <div class="w-6 h-6 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center shadow-sm transition-transform group-hover:scale-110">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs text-white/60 uppercase tracking-wide font-medium">Teléfono</p>
                                <p class="text-xs font-bold text-white">{{ $client->telefono ?? 'No registrado' }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Email -->
                    <div class="info-card group">
                        <div class="flex items-center space-x-1.5">
                            <div class="w-6 h-6 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center shadow-sm transition-transform group-hover:scale-110">
                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs text-white/60 uppercase tracking-wide font-medium">Email</p>
                                <p class="text-xs font-bold text-white break-all">{{ $client->email ?? 'No registrado' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>          
  <!-- Lista de Contratos -->
            <section class="glass-card p-3 animate-fade-up" style="animation-delay: 0.2s">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <div class="w-7 h-7 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-lg flex items-center justify-center shadow-md">
                            <svg class="w-3.5 h-3.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div>
                            <h2 class="text-sm font-bold text-white font-display">Portfolio de Contratos</h2>
                            <p class="text-white/70 text-xs">Gestión de pagos y obligaciones</p>
                        </div>
                    </div>
                    
                    <div class="text-right">
                        <div class="text-xs text-white/60 mb-0.5">Total</div>
                        <div class="text-sm font-bold text-white">{{ $contracts->count() }}</div>
                        <div class="status-badge status-active mt-0.5 text-xs">
                            <span class="font-medium">Activos</span>
                        </div>
                    </div>
                </div>

                @if($contracts->count() > 0)
                    <div class="space-y-2 stagger-children">
                        @foreach($contracts as $contract)
                            <div class="glass-card p-2.5">
                                <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-2 mb-2">
                                    <div>
                                        <div class="flex items-center mb-0.5">
                                            <h3 class="text-xs font-bold text-white">Contrato #{{ $contract->id }}</h3>
                                            <span class="status-badge ml-1.5 {{ $contract->estado_formateado['clase'] ?? 'status-active' }}">
                                                {{ $contract->estado_formateado['texto'] ?? ucfirst($contract->estado) }}
                                                @if(isset($contract->estado_formateado['dias']))
                                                    <span class="ml-1 opacity-75">
                                                        ({{ $contract->estado_formateado['dias'] }})
                                                    </span>
                                                @endif
                                            </span>
                                        </div>
                                        <p class="text-white/70 flex items-center text-xs">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 2m8-2l2 2m-2-2v6a2 2 0 01-2 2H10a2 2 0 01-2-2v-6" />
                                            </svg>
                                            Inicio: <span class="font-medium">{{ date('d/m/Y', strtotime($contract->fecha_inicio)) }}</span>
                                        </p>
                                    </div>
                                    <div class="info-card">
                                        <p class="text-xs text-white/70">Monto Total</p>
                                        <p class="text-lg font-bold text-white">${{ number_format($contract->monto_total, 2) }}</p>
                                    </div>
                                </div>

                                <!-- Progreso de pago -->
                                <div class="mb-3">
                                    <div class="flex justify-between items-center mb-2">
                                        <div class="text-xs font-medium flex items-center text-white">
                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                            </svg>
                                            Progreso: <span class="text-blue-300 font-semibold ml-1">{{ round(($contract->cuotas_pagadas / $contract->numero_cuotas) * 100) }}%</span>
                                        </div>
                                        <div class="text-xs text-white/70 bg-white/10 px-2 py-1 rounded">
                                            {{ $contract->cuotas_pagadas }}/{{ $contract->numero_cuotas }}
                                        </div>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: {{ ($contract->cuotas_pagadas / $contract->numero_cuotas) * 100 }}%"></div>
                                    </div>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-3 gap-2">
                                    <div class="info-card">
                                        <div class="flex items-center mb-1">
                                            <svg class="w-3 h-3 text-green-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-xs text-white/70">Pagado</p>
                                        </div>
                                        <p class="font-bold text-white text-sm">${{ number_format($contract->monto_total - $contract->monto_pendiente, 2) }}</p>
                                    </div>
                                    <div class="info-card">
                                        <div class="flex items-center mb-1">
                                            <svg class="w-3 h-3 text-yellow-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <p class="text-xs text-white/70">Pendiente</p>
                                        </div>
                                        <p class="font-bold text-white text-sm">${{ number_format($contract->monto_pendiente, 2) }}</p>
                                    </div>
                                    <div class="info-card">
                                        <div class="flex items-center mb-1">
                                            <svg class="w-3 h-3 text-blue-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M8 7V3a2 2 0 012-2h4a2 2 0 012 2v4m-6 0h6m-6 0l-2 2m8-2l2 2m-2-2v6a2 2 0 01-2 2H10a2 2 0 01-2-2v-6" />
                                            </svg>
                                            <p class="text-xs text-white/70">Próximo</p>
                                        </div>
                                        <p class="font-bold text-white text-sm">
                                            @if(isset($contract->proximo_vencimiento) && $contract->proximo_vencimiento)
                                                {{ $contract->proximo_vencimiento->format('d/m/Y') }}
                                            @else
                                                N/A
                                            @endif
                                        </p>
                                    </div>
                                </div>

                                <!-- Estado de Firma -->
                                @php
                                    $isSigned = !empty($contract->firma);
                                    $hasSignatureDate = false; // No tenemos fecha_firma en la BD
                                    $hasSignedFile = !empty($contract->firma);
                                @endphp
                                
                                <div class="mt-3 mb-3">
                                    <div class="signature-section">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                @if($isSigned)
                                                    <div class="w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center shadow-lg">
                                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <p class="text-sm font-bold text-white flex items-center">
                                                            Contrato Firmado
                                                            <svg class="w-3 h-3 ml-1 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                                            </svg>
                                                        </p>
                                                        <p class="text-xs text-white/70">
                                                            @if($hasSignatureDate)
                                                                Firmado el {{ $contract->fecha_firma->format('d/m/Y H:i') }}
                                                            @else
                                                                Documento firmado digitalmente
                                                            @endif
                                                        </p>
                                                    </div>
                                                @else
                                                    <div class="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center shadow-lg animate-pulse">
                                                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                                        </svg>
                                                    </div>
                                                    <div>
                                                        <p class="text-sm font-bold text-white flex items-center">
                                                            Pendiente de Firma
                                                            <svg class="w-3 h-3 ml-1 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                        </p>
                                                        <p class="text-xs text-white/70">El contrato requiere firma digital</p>
                                                    </div>
                                                @endif
                                            </div>
                                            
                                            <div class="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                                                <!-- Badge de Estado -->
                                                <div class="flex items-center">
                                                    @if($isSigned)
                                                        <div class="status-badge status-completado">
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                            Firmado
                                                        </div>
                                                    @else
                                                        <div class="status-badge status-pending">
                                                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                                <path stroke-linecap="round" stroke-linejoin="round" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                            </svg>
                                                            Pendiente
                                                        </div>
                                                    @endif
                                                </div>

                                                <!-- Botones de Acción -->
                                                <div class="signature-actions">
                                                    <!-- Botón de Descarga PDF -->
                                                    <a href="{{ route('contracts.pdf', $contract->id) }}?cedula={{ $cedula }}" 
                                                       class="morphism-button signature-button-signed group text-xs"
                                                       target="_blank"
                                                       title="Descargar contrato en PDF">
                                                        <svg class="w-3 h-3 mr-1 transition-transform group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                        </svg>
                                                        PDF
                                                    </a>

                                                    <!-- Botón de Firma (siempre visible) -->
                                                    <a href="{{ route('contracts.firmar', $contract->id) }}?cedula={{ $cedula }}"
                                                       class="morphism-button {{ $isSigned ? 'signature-button-signed' : 'signature-button-pending' }} group text-xs"
                                                       title="{{ $isSigned ? 'Ver contrato firmado' : 'Firmar el contrato digitalmente' }}">
                                                        <svg class="w-3 h-3 mr-1 transition-transform group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                            <path stroke-linecap="round" stroke-linejoin="round" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                                        </svg>
                                                        @if($isSigned)
                                                            Ver Firma
                                                        @else
                                                            Firmar
                                                        @endif
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Historial de Pagos -->
                                @if($contract->payments->count() > 0)
                                    <div class="mt-3">
                                        <div class="flex items-center mb-2">
                                            <div class="w-6 h-6 bg-white/20 rounded flex items-center justify-center mr-2">
                                                <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                                </svg>
                                            </div>
                                            <h4 class="text-sm font-bold text-white">Historial de Pagos</h4>
                                        </div>
                                        <div class="table-glass">
                                            <table class="min-w-full">
                                                <thead>
                                                    <tr>
                                                        <th class="text-xs">Cuota</th>
                                                        <th class="text-xs">Fecha</th>
                                                        <th class="text-xs">Monto</th>
                                                        <th class="text-xs">Método</th>
                                                        <th class="text-xs">Ref.</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($contract->payments as $payment)
                                                        <tr>
                                                            <td class="font-semibold text-xs">{{ $payment->numero_cuota }}</td>
                                                            <td class="text-xs">{{ $payment->fecha_pago->format('d/m/Y') }}</td>
                                                            <td class="font-bold text-green-300 text-xs">${{ number_format($payment->monto, 2) }}</td>
                                                            <td class="text-xs">{{ $payment->metodo_pago ?? 'Efectivo' }}</td>
                                                            <td class="text-white/70 text-xs">{{ $payment->referencia ?? 'N/A' }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                @else
                                    <div class="info-card text-center">
                                        <p class="text-white/70 italic">No hay pagos registrados para este contrato.</p>
                                    </div>
                                @endif
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="info-card text-center">
                        <p class="text-white/70 italic text-lg">No hay contratos registrados para este cliente.</p>
                    </div>
                @endif
            </section>
        </div>
    </main>
</body>
</html>
