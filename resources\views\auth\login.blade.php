<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ config('app.name', 'Laravel') }} - Iniciar <PERSON><PERSON></title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Outfit:wght@500;600;700;800&display=swap" rel="stylesheet">
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <style>
        :root {
            /* Professional Dark Glass Morphism */
            --primary-dark: #0a0a0a;
            --secondary-dark: #1a1a1a;
            --accent-dark: #2a2a2a;
            --border-dark: #333333;

            /* Glass Morphism - Dark Professional */
            --glass-bg: rgba(10, 10, 10, 0.7);
            --glass-bg-light: rgba(26, 26, 26, 0.6);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-border-hover: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.9);
            --glass-shadow-hover: 0 35px 60px -12px rgba(0, 0, 0, 0.95);

            /* Professional Typography */
            --text-primary: #ffffff;
            --text-secondary: #e5e5e5;
            --text-muted: #a3a3a3;
            --text-accent: #3b82f6;

            /* Modern Gradients */
            --bg-gradient: linear-gradient(135deg, #000000 0%, #1a1a1a 50%, #0a0a0a 100%);
            --card-gradient: linear-gradient(145deg, rgba(26, 26, 26, 0.8) 0%, rgba(10, 10, 10, 0.9) 100%);
            --accent-gradient: linear-gradient(90deg, #3b82f6 0%, #1d4ed8 100%);
            --button-gradient: linear-gradient(135deg, #1e40af 0%, #1d4ed8 100%);

            /* Professional Effects */
            --shadow-glass: 0 25px 50px -12px rgba(0, 0, 0, 0.9);
            --shadow-button: 0 10px 25px rgba(59, 130, 246, 0.3);
            --shadow-input: inset 0 2px 4px rgba(0, 0, 0, 0.3);

            /* Smooth Animations */
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }

        /* Professional Base Styles */
        body {
            font-family: 'Inter', system-ui, sans-serif;
            color: var(--text-primary);
            line-height: 1.5;
            min-height: 100vh;
            overflow-x: hidden;
            background: var(--primary-dark);
        }

        /* Dark Professional Background */
        .login-background {
            background: var(--bg-gradient);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* Subtle animated overlay */
        .login-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(29, 78, 216, 0.1) 0%, transparent 50%);
            animation: subtleFloat 20s ease-in-out infinite;
            pointer-events: none;
        }

        @keyframes subtleFloat {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
                opacity: 0.8;
            }
            50% {
                transform: translateY(-10px) rotate(1deg);
                opacity: 1;
            }
        }

        /* Decorative elements */
        .shape {
            position: absolute;
            pointer-events: none;
            z-index: 0;
        }

        .shape-1 {
            top: 15%;
            left: 10%;
            width: 300px;
            height: 300px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation: floating 15s ease-in-out infinite;
        }

        .shape-2 {
            bottom: 10%;
            right: 10%;
            width: 400px;
            height: 400px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
            animation: floating 18s ease-in-out infinite reverse;
        }

        .shape-3 {
            top: 50%;
            right: 20%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50% 50% 50% 50% / 60% 40% 60% 40%;
            animation: floating 12s ease-in-out infinite;
        }

        @keyframes floating {
            0% { transform: translateY(0) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
            100% { transform: translateY(0) rotate(0deg); }
        }

        /* Professional Login Container */
        .login-container {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: 16px;
            box-shadow: var(--shadow-glass);
            padding: 2.5rem 2rem;
            margin: 1rem;
            position: relative;
            overflow: hidden;
            animation: fadeInUp 0.6s ease-out forwards;
            z-index: 10;
            max-width: 380px;
            width: 100%;
            transition: var(--transition);
        }

        .login-container:hover {
            border-color: var(--glass-border-hover);
            box-shadow: var(--glass-shadow-hover);
            transform: translateY(-2px);
        }

        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--accent-gradient);
            z-index: 1;
            border-radius: 16px 16px 0 0;
        }

        /* Professional Typography */
        .login-title {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 1.75rem;
            color: var(--text-primary);
            margin-bottom: 0.5rem;
            letter-spacing: -0.01em;
            text-align: center;
        }

        .login-subtitle {
            color: var(--text-muted);
            font-size: 0.95rem;
            margin-bottom: 2rem;
            text-align: center;
            font-weight: 400;
        }

        /* Professional Form Elements */
        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .input-wrapper {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            pointer-events: none;
            transition: var(--transition);
            z-index: 2;
        }

        .form-input {
            width: 100%;
            padding: 0.875rem 1rem 0.875rem 2.75rem;
            font-size: 0.95rem;
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            background: var(--glass-bg-light);
            backdrop-filter: blur(10px);
            transition: var(--transition);
            color: var(--text-primary);
            box-shadow: var(--shadow-input);
        }

        .form-input::placeholder {
            color: var(--text-muted);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--text-accent);
            box-shadow: var(--shadow-input), 0 0 0 3px rgba(59, 130, 246, 0.2);
            background: rgba(26, 26, 26, 0.8);
        }

        .form-input:focus + .input-icon {
            color: var(--text-accent);
        }

        /* Professional Checkbox */
        .checkbox-wrapper {
            display: flex;
            align-items: center;
            margin-bottom: 1.5rem;
        }

        .remember-checkbox {
            position: relative;
            width: 1.125rem;
            height: 1.125rem;
            border-radius: 4px;
            border: 1px solid var(--glass-border);
            background: var(--glass-bg-light);
            appearance: none;
            -webkit-appearance: none;
            -moz-appearance: none;
            cursor: pointer;
            transition: var(--transition);
            flex-shrink: 0;
        }

        .remember-checkbox:checked {
            background: var(--accent-gradient);
            border-color: var(--text-accent);
        }

        .remember-checkbox:checked::after {
            content: '';
            position: absolute;
            top: 35%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(45deg);
            width: 0.2rem;
            height: 0.4rem;
            border: solid white;
            border-width: 0 1.5px 1.5px 0;
        }

        .remember-label {
            margin-left: 0.625rem;
            font-size: 0.875rem;
            color: var(--text-secondary);
            cursor: pointer;
            font-weight: 400;
        }

        /* Professional Login Button */
        .login-button {
            width: 100%;
            padding: 0.875rem 1.5rem;
            font-size: 0.95rem;
            font-weight: 600;
            color: white;
            background: var(--button-gradient);
            border: 1px solid var(--text-accent);
            border-radius: 8px;
            cursor: pointer;
            transition: var(--transition-spring);
            position: relative;
            overflow: hidden;
            z-index: 1;
            box-shadow: var(--shadow-button);
            display: flex;
            align-items: center;
            justify-content: center;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .login-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #1d4ed8 0%, #2563eb 100%);
            opacity: 0;
            z-index: -1;
            transition: var(--transition);
        }

        .login-button:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 15px 30px rgba(59, 130, 246, 0.4);
            border-color: #60a5fa;
        }

        .login-button:hover::before {
            opacity: 1;
        }

        .login-button:active {
            transform: translateY(0) scale(0.98);
        }

        .button-icon {
            margin-right: 0.75rem;
        }

        /* Forgot password link */
        .forgot-link {
            color: var(--primary-gradient-start);
            font-size: 0.95rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.2s ease;
            position: relative;
        }

        .forgot-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary-gradient-start), var(--primary-gradient-end));
            transition: width 0.3s ease;
        }

        .forgot-link:hover::after {
            width: 100%;
        }

        /* Professional Error Messages */
        .error-message {
            color: #ef4444;
            font-size: 0.8rem;
            margin-top: 0.375rem;
            display: flex;
            align-items: center;
            background: rgba(239, 68, 68, 0.1);
            padding: 0.5rem 0.75rem;
            border-radius: 6px;
            border-left: 3px solid #ef4444;
        }

        .error-message::before {
            content: '⚠';
            margin-right: 0.5rem;
            font-size: 0.875rem;
            font-weight: bold;
        }

        /* Professional Responsive Design */
        @media (max-width: 640px) {
            .login-container {
                padding: 2rem 1.5rem;
                margin: 0.5rem;
                max-width: 350px;
            }

            .login-title {
                font-size: 1.5rem;
            }

            .login-subtitle {
                font-size: 0.875rem;
                margin-bottom: 1.5rem;
            }

            .form-group {
                margin-bottom: 1.25rem;
            }

            .form-input {
                padding: 0.75rem 0.875rem 0.75rem 2.5rem;
                font-size: 0.9rem;
            }
        }

        /* Professional Accessibility */
        :focus-visible {
            outline: 2px solid var(--text-accent);
            outline-offset: 2px;
            border-radius: 4px;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        /* Reduced motion for accessibility */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
    </style>
</head>
<body>
    <div class="login-background">
        <!-- Elementos decorativos -->
        <div class="shape shape-1" aria-hidden="true"></div>
        <div class="shape shape-2" aria-hidden="true"></div>
        <div class="shape shape-3" aria-hidden="true"></div>

        <div class="min-h-screen flex flex-col sm:justify-center items-center p-4">
            <div class="w-full sm:max-w-md login-container rounded-2xl">
                <div class="mb-6 text-center">
                    <h2 class="login-title">Acceso Administrativo</h2>
                    <p class="login-subtitle">Sistema de Gestión de Pagos</p>
                </div>

    <!-- Session Status -->
                @if (session('status'))
                    <div class="mb-6 p-4 bg-blue-50 border-l-4 border-blue-500 text-blue-700 rounded-lg">
                        {{ session('status') }}
                    </div>
                @endif

    <form method="POST" action="{{ route('login') }}">
        @csrf

        <!-- Email Address -->
                    <div class="form-group">
                        <label for="email" class="form-label">
                            Correo Electrónico
                        </label>
                        <div class="input-wrapper">
                            <input
                                id="email"
                                type="email"
                                name="email"
                                value="{{ old('email') }}"
                                required
                                autofocus
                                autocomplete="username"
                                class="form-input"
                                placeholder="<EMAIL>"
                            >
                            <svg class="input-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                        </div>
                        @error('email')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
        </div>

        <!-- Password -->
                    <div class="form-group">
                        <label for="password" class="form-label">
                            Contraseña
                        </label>
                        <div class="input-wrapper">
                            <input
                                id="password"
                            type="password"
                            name="password"
                                required
                                autocomplete="current-password"
                                class="form-input"
                                placeholder="••••••••"
                            >
                            <svg class="input-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                            </svg>
                        </div>
                        @error('password')
                            <div class="error-message">{{ $message }}</div>
                        @enderror
        </div>

                    <!-- Remember Me and Forgot Password -->
                    <div class="flex items-center justify-between mb-8">
                        <div class="checkbox-wrapper">
                            <input
                                id="remember_me"
                                type="checkbox"
                                name="remember"
                                class="remember-checkbox"
                            >
                            <label for="remember_me" class="remember-label">Recordarme</label>
        </div>

            @if (Route::has('password.request'))
                            <a class="forgot-link" href="{{ route('password.request') }}">
                                ¿Olvidaste tu contraseña?
                </a>
            @endif
                    </div>

                    <button type="submit" class="login-button">
                        <svg class="button-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                            <polyline points="10 17 15 12 10 7"></polyline>
                            <line x1="15" y1="12" x2="3" y2="12"></line>
                        </svg>
                        Iniciar Sesión
                    </button>
                </form>
            </div>
        </div>
    </div>
</body>
</html>