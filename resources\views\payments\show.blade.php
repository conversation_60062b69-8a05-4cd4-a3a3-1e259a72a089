<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Detalles del Pago') }}
            </h2>
            <div class="flex space-x-4">
                <a href="{{ route('payments.edit', $payment) }}" 
                    class="bg-indigo-600 py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Editar Pago
                </a>
                <a href="{{ route('payments.index') }}" 
                    class="bg-gray-200 py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                    Volver a la Lista
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Información del Pago</h3>
                            <dl class="grid grid-cols-1 gap-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">ID del Pago</dt>
                                    <dd class="mt-1 text-sm text-gray-900">#{{ $payment->id }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Monto</dt>
                                    <dd class="mt-1 text-sm text-gray-900">${{ number_format($payment->monto, 2) }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Fecha de Pago</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $payment->fecha_pago->format('d/m/Y') }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Método de Pago</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $payment->metodo_pago }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Referencia</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $payment->referencia ?: 'N/A' }}</dd>
                                </div>
                            </dl>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Información del Contrato</h3>
                            <dl class="grid grid-cols-1 gap-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">ID del Contrato</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        <a href="{{ route('contracts.show', $payment->contract) }}" class="text-indigo-600 hover:text-indigo-900">
                                            #{{ $payment->contract->id }}
                                        </a>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Cliente</dt>
                                    <dd class="mt-1 text-sm text-gray-900">
                                        <a href="{{ route('clients.show', $payment->contract->client) }}" class="text-indigo-600 hover:text-indigo-900">
                                            {{ $payment->contract->client->nombre_completo }}
                                        </a>
                                    </dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Número de Cuota</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $payment->numero_cuota }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Monto Total del Contrato</dt>
                                    <dd class="mt-1 text-sm text-gray-900">${{ number_format($payment->contract->monto_total, 2) }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Monto Pendiente</dt>
                                    <dd class="mt-1 text-sm text-gray-900">${{ number_format($payment->contract->monto_pendiente, 2) }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    @if($payment->notas)
                        <div class="mt-8">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Notas</h3>
                            <div class="bg-gray-50 rounded-lg p-4">
                                <p class="text-sm text-gray-700 whitespace-pre-line">{{ $payment->notas }}</p>
                            </div>
                        </div>
                    @endif

                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Información del Sistema</h3>
                        <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Creado</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $payment->created_at->format('d/m/Y H:i:s') }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-gray-500">Última Actualización</dt>
                                <dd class="mt-1 text-sm text-gray-900">{{ $payment->updated_at->format('d/m/Y H:i:s') }}</dd>
                            </div>
                        </dl>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout> 