<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Firmar contrato">
    <title>Firmar Contrato</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800&family=Open+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <!-- SweetAlert2 -->
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#f0f6ff',
                            100: '#e0ecff',
                            200: '#c7d9ff',
                            300: '#a3bdff',
                            400: '#7a97ff',
                            500: '#5b6cf9',
                            600: '#3a44f0',
                            700: '#2e36d4',
                            800: '#2830ab',
                            900: '#1a56db',
                            950: '#1e429f',
                        }
                    },
                    fontFamily: {
                        sans: ['Open Sans', 'sans-serif'],
                        display: ['Montserrat', 'sans-serif'],
                    },
                    boxShadow: {
                        'glass': '0 8px 32px rgba(0, 0, 0, 0.1)',
                    },
                }
            }
        }
    </script>
    <style>
        /* Fondo con imagen y efecto glass */
        body {
            background-image: url('https://images.unsplash.com/photo-1557683316-973673baf926?q=80&w=2029');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            font-family: 'Open Sans', sans-serif;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(26, 86, 219, 0.8);
            z-index: 0;
        }

        /* Efecto glass */
        .glass-effect {
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Firma digital */
        .signature-container {
            border: 2px dashed #cbd5e1;
            border-radius: 0.5rem;
            background-color: rgba(255, 255, 255, 0.9);
            text-align: center;
            transition: all 0.3s ease;
        }

        .signature-container:hover {
            border-color: #1a56db;
        }

        /* Animaciones */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .animate-fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        .animate-slide-up {
            animation: slideUp 0.5s ease-out;
        }
    </style>
</head>
<body class="font-sans text-gray-800 antialiased">
    <div class="min-h-screen flex flex-col relative z-10">
        <!-- Header -->
        <header class="text-white shadow-lg relative z-20">
            <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="mr-4 bg-white bg-opacity-20 p-3 rounded-lg shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                                <polyline points="10 17 15 12 10 7"></polyline>
                                <line x1="15" y1="12" x2="3" y2="12"></line>
                            </svg>
                        </div>
                        <div>
                            <h1 class="font-display font-bold text-xl sm:text-2xl">
                                Firma Digital de Contrato
                            </h1>
                            <div class="flex items-center mt-1">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                    <line x1="16" y1="2" x2="16" y2="6"></line>
                                    <line x1="8" y1="2" x2="8" y2="6"></line>
                                    <line x1="3" y1="10" x2="21" y2="10"></line>
                                </svg>
                                <p class="text-white text-opacity-90 text-sm sm:text-base">
                                    Contrato #{{ $contract->id }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <a href="{{ route('public.search.results', ['cedula' => request('cedula')]) }}" class="glass-effect bg-white bg-opacity-10 hover:bg-opacity-20 text-white font-medium py-2 px-4 rounded-lg transition duration-300 flex items-center justify-center shadow-md">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <polyline points="15 18 9 12 15 6"></polyline>
                        </svg>
                        Volver
                    </a>
                </div>
            </div>
        </header>

        <main class="flex-grow py-8 sm:py-12">
            <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="glass-effect rounded-xl shadow-glass overflow-hidden animate-fade-in p-8">
                    <div class="text-center mb-6">
                        <div class="bg-primary-900 bg-opacity-10 inline-flex p-3 rounded-full mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-primary-900" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                                <polyline points="10 17 15 12 10 7"></polyline>
                                <line x1="15" y1="12" x2="3" y2="12"></line>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Firma Digital de Contrato</h2>
                        <p class="text-gray-600 max-w-md mx-auto">
                            Por favor, dibuja tu firma en el recuadro a continuación y haz clic en "Guardar Firma" para completar el proceso.
                        </p>
                    </div>

                    <form id="firmaForm" method="POST" action="{{ route('contracts.guardarFirma', $contract) }}?cedula={{ request('cedula') }}" class="animate-slide-up" style="animation-delay: 0.2s">
                        @csrf
                        <input type="hidden" name="cedula" value="{{ request('cedula') }}">
                        <div class="mb-6">
                            <div class="signature-container p-4 mb-2">
                                <canvas id="signature-pad" width="100%" height="200" style="border:1px solid #e2e8f0; background:#ffffff; border-radius: 0.375rem; width: 100%; touch-action: none;"></canvas>
                                <input type="hidden" name="firma" id="firmaInput">
                            </div>
                            <p class="text-sm text-gray-500 text-center">
                                Utiliza el mouse o tu dedo para firmar en el recuadro
                            </p>
                        </div>

                        <div class="flex flex-col sm:flex-row gap-3 justify-center">
                            <button type="button" id="clear" class="glass-effect bg-gray-600 bg-opacity-90 hover:bg-opacity-100 text-white font-medium py-3 px-6 rounded-lg shadow-sm transition duration-300 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M19 12H5"></path>
                                </svg>
                                Limpiar Firma
                            </button>
                            <button type="submit" class="glass-effect bg-primary-900 bg-opacity-90 hover:bg-opacity-100 text-white font-medium py-3 px-6 rounded-lg shadow-sm transition duration-300 flex items-center justify-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"></path>
                                    <polyline points="17 21 17 13 7 13 7 21"></polyline>
                                    <polyline points="7 3 7 8 15 8"></polyline>
                                </svg>
                                Guardar Firma
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.1.6/dist/signature_pad.umd.min.js"></script>
    <script>
        // Configuración personalizada para SweetAlert2
        const SwalGlassConfig = {
            background: 'rgba(255, 255, 255, 0.85)',
            backdrop: 'rgba(26, 86, 219, 0.4)',
            confirmButtonColor: '#1a56db',
            showClass: {
                popup: 'animate__animated animate__fadeIn animate__faster'
            },
            hideClass: {
                popup: 'animate__animated animate__fadeOut animate__faster'
            },
            customClass: {
                popup: 'swal-glass'
            }
        };

        // Agregar estilos para SweetAlert2
        const style = document.createElement('style');
        style.textContent = `
            .swal-glass {
                backdrop-filter: blur(10px);
                -webkit-backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.18) !important;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
            }
            .swal2-title, .swal2-html-container {
                color: #1e293b !important;
            }
        `;
        document.head.appendChild(style);

        // Inicializar el pad de firma
        const canvas = document.getElementById('signature-pad');
        const signaturePad = new SignaturePad(canvas, {
            backgroundColor: 'rgb(255, 255, 255)',
            penColor: 'rgb(0, 0, 0)'
        });

        // Ajustar el tamaño del canvas al tamaño del contenedor
        function resizeCanvas() {
            const ratio = Math.max(window.devicePixelRatio || 1, 1);
            canvas.width = canvas.offsetWidth * ratio;
            canvas.height = canvas.offsetHeight * ratio;
            canvas.getContext("2d").scale(ratio, ratio);
            signaturePad.clear(); // Limpiar el canvas después de redimensionar
        }

        // Redimensionar el canvas cuando cambia el tamaño de la ventana
        window.addEventListener("resize", resizeCanvas);
        resizeCanvas(); // Llamar una vez al cargar

        // Limpiar la firma
        document.getElementById('clear').addEventListener('click', function(e) {
            e.preventDefault();
            signaturePad.clear();
        });

        // Validar y enviar el formulario
        document.getElementById('firmaForm').addEventListener('submit', function(e) {
            if (signaturePad.isEmpty()) {
                e.preventDefault();
                Swal.fire({
                    ...SwalGlassConfig,
                    icon: 'warning',
                    title: 'Firma requerida',
                    text: 'Por favor, realiza tu firma antes de guardar.'
                });
                return false;
            }
            document.getElementById('firmaInput').value = signaturePad.toDataURL();
        });
    </script>
</body>
</html>