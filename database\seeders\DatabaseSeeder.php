<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // User::factory(10)->create();

        User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
        ]);

        // Crear usuario administrador
        User::create([
            'name' => 'Administrador',
            'email' => '<EMAIL>',
            'password' => Hash::make('ALEjandro@2025'),
        ]);

        // Crear datos de prueba
        $this->call([
            TestDataSeeder::class,
        ]);
    }
}
