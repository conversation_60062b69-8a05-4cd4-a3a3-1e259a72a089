<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="flex justify-between items-center mb-6">
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('Pagos') }}
                </h2>
                <a href="{{ route('payments.create') }}" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                    Crear <PERSON>
                </a>
            </div>
            <div class="bg-white overflow-hidden shadow-pro rounded-lg">
                <div class="p-6 text-gray-900">
                    @if(session('success'))
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4" role="alert">
                            <span class="block sm:inline">{{ session('success') }}</span>
                        </div>
                    @endif

                    <div class="overflow-x-auto">
                        <table class="datatable min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cliente</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contrato</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cuota</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Monto</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fecha</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Método</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Acciones</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @forelse($payments as $payment)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <a href="{{ route('clients.show', $payment->contract->client) }}" class="text-indigo-600 hover:text-indigo-900">
                                                {{ $payment->contract->client->nombre_completo }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <a href="{{ route('contracts.show', $payment->contract) }}" class="text-indigo-600 hover:text-indigo-900">
                                                #{{ $payment->contract->id }}
                                            </a>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $payment->numero_cuota }} de {{ $payment->contract->numero_cuotas }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">${{ number_format($payment->monto, 2) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $payment->fecha_pago->format('d/m/Y') }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap">{{ $payment->metodo_pago ?? 'N/A' }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                            <a href="{{ route('payments.show', $payment) }}" 
                                               class="text-indigo-600 hover:text-indigo-900 mr-3">
                                                Ver
                                            </a>
                                            <a href="{{ route('payments.edit', $payment) }}" 
                                               class="text-yellow-600 hover:text-yellow-900 mr-3">
                                                Editar
                                            </a>
                                            <button type="button" 
                                                    onclick="confirmDelete('{{ $payment->id }}')"
                                                    class="text-red-600 hover:text-red-900">
                                                Eliminar
                                            </button>
                                            <form id="delete-form-{{ $payment->id }}" 
                                                  action="{{ route('payments.destroy', $payment) }}" 
                                                  method="POST" 
                                                  class="hidden">
                                                @csrf
                                                @method('DELETE')
                                            </form>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="7" class="px-6 py-4 whitespace-nowrap text-center text-gray-500">
                                            No hay pagos registrados.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4">
                        {{ $payments->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        // Inicializar DataTables
        $(document).ready(function() {
            $('.datatable').DataTable({
                responsive: true,
                language: {
                    url: 'https://cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
                },
                order: [[4, 'desc']], // Ordenar por fecha de pago descendente
                pageLength: 10,
                columnDefs: [
                    {
                        targets: -1,
                        orderable: false,
                        searchable: false
                    }
                ]
            });
        });

        // Función para confirmar eliminación
        function confirmDelete(paymentId) {
            Swal.fire({
                title: '¿Está seguro?',
                text: "Esta acción no se puede deshacer",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Sí, eliminar',
                cancelButtonText: 'Cancelar'
            }).then((result) => {
                if (result.isConfirmed) {
                    document.getElementById(`delete-form-${paymentId}`).submit();
                }
            });
        }

        // Mostrar notificaciones de éxito/error
        @if(session('success'))
            @if(is_array(session('success')))
                Swal.fire({
                    title: "{{ session('success')['title'] }}",
                    text: "{{ session('success')['text'] }}",
                    icon: 'success',
                    timer: 3000,
                    timerProgressBar: true
                });
            @else
                Toast.fire({
                    icon: 'success',
                    title: "{{ session('success') }}"
                });
            @endif
        @endif

        @if(session('error'))
            Swal.fire({
                title: 'Error',
                text: "{{ session('error') }}",
                icon: 'error'
            });
        @endif
    </script>
    @endpush
</x-app-layout> 