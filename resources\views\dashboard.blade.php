@extends('layouts.dashboard')

@section('content')
<div class="dashboard-main-content">
    <!-- <PERSON>er -->
    <div class="dashboard-header">
        <div class="header-content">
            <h1 class="dashboard-title">Panel de Control</h1>
            <p class="dashboard-subtitle">Resumen general del sistema de pagos</p>
        </div>
        <div class="header-actions">
            <div class="date-info">
                <i class="fas fa-calendar-alt"></i>
                <span>{{ now()->format('d/m/Y') }}</span>
            </div>
        </div>
    </div>

    <!-- Estadísticas Principales -->
    <div class="dashboard-stats">
        <div class="stat-card primary">
            <div class="stat-icon">
                <i class="fas fa-users"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-number">{{ number_format($estadisticas['totalClientes']) }}</h3>
                <p class="stat-label">Total Clientes</p>
                <div class="stat-trend positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+12% este mes</span>
                </div>
            </div>
        </div>

        <div class="stat-card success">
            <div class="stat-icon">
                <i class="fas fa-file-contract"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-number">{{ number_format($estadisticas['totalContratos']) }}</h3>
                <p class="stat-label">Total Contratos</p>
                <div class="stat-trend positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+8% este mes</span>
                </div>
            </div>
        </div>

        <div class="stat-card warning">
            <div class="stat-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-number">{{ number_format($estadisticas['totalPagos']) }}</h3>
                <p class="stat-label">Total Pagos</p>
                <div class="stat-trend positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+15% este mes</span>
                </div>
            </div>
        </div>

        <div class="stat-card info">
            <div class="stat-icon">
                <i class="fas fa-dollar-sign"></i>
            </div>
            <div class="stat-info">
                <h3 class="stat-number">${{ number_format($estadisticas['montoTotalPagos'], 0) }}</h3>
                <p class="stat-label">Total Recaudado</p>
                <div class="stat-trend positive">
                    <i class="fas fa-arrow-up"></i>
                    <span>+22% este mes</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Resumen Financiero -->
    <div class="financial-summary">
        <div class="summary-card">
            <div class="summary-header">
                <h3>Resumen Financiero</h3>
                <div class="summary-period">
                    <select class="period-selector">
                        <option>Este mes</option>
                        <option>Últimos 3 meses</option>
                        <option>Este año</option>
                    </select>
                </div>
            </div>
            <div class="summary-metrics">
                <div class="metric">
                    <div class="metric-icon success">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="metric-data">
                        <span class="metric-value">${{ number_format($estadisticas['montoTotalContratos'], 0) }}</span>
                        <span class="metric-label">Monto Total Contratos</span>
                    </div>
                </div>
                <div class="metric">
                    <div class="metric-icon primary">
                        <i class="fas fa-hand-holding-usd"></i>
                    </div>
                    <div class="metric-data">
                        <span class="metric-value">${{ number_format($estadisticas['montoTotalPagos'], 0) }}</span>
                        <span class="metric-label">Total Recaudado</span>
                    </div>
                </div>
                <div class="metric">
                    <div class="metric-icon danger">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="metric-data">
                        <span class="metric-value">${{ number_format($estadisticas['montoPendiente'], 0) }}</span>
                        <span class="metric-label">Monto Pendiente</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Gráficos y Análisis -->
    <div class="charts-section">
        <div class="charts-grid">
            <!-- Gráfico de Pagos Mensuales -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-line"></i>
                        Pagos Mensuales
                    </h3>
                    <div class="chart-actions">
                        <button class="chart-btn active" data-period="month">Mes</button>
                        <button class="chart-btn" data-period="year">Año</button>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="pagosMensualesChart"></canvas>
                </div>
            </div>

            <!-- Gráfico de Estado de Contratos -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">
                        <i class="fas fa-chart-pie"></i>
                        Estado de Contratos
                    </h3>
                    <div class="chart-legend">
                        <div class="legend-item">
                            <span class="legend-color active"></span>
                            <span>Activos</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color warning"></span>
                            <span>Por Vencer</span>
                        </div>
                        <div class="legend-item">
                            <span class="legend-color danger"></span>
                            <span>Vencidos</span>
                        </div>
                    </div>
                </div>
                <div class="chart-container">
                    <canvas id="estadoContratosChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Tablas de Datos -->
    <div class="data-tables">
        <div class="tables-grid">
            <!-- Pagos Recientes -->
            <div class="table-card">
                <div class="table-header">
                    <h3 class="table-title">
                        <i class="fas fa-clock"></i>
                        Pagos Recientes
                    </h3>
                    <a href="/payments" class="view-all-btn">Ver todos</a>
                </div>
                <div class="table-content">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Cliente</th>
                                    <th>Monto</th>
                                    <th>Fecha</th>
                                    <th>Estado</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($pagosRecientes as $pago)
                                <tr>
                                    <td>
                                        <div class="client-info">
                                            <div class="client-avatar">
                                                {{ substr($pago['cliente'], 0, 1) }}
                                            </div>
                                            <span class="client-name">{{ $pago['cliente'] }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="amount">${{ number_format($pago['monto'], 0) }}</span>
                                    </td>
                                    <td>
                                        <span class="date">{{ $pago['fecha'] }}</span>
                                    </td>
                                    <td>
                                        <span class="status-badge success">Completado</span>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Contratos Próximos a Vencer -->
            <div class="table-card">
                <div class="table-header">
                    <h3 class="table-title">
                        <i class="fas fa-calendar-exclamation"></i>
                        Contratos por Vencer
                    </h3>
                    <a href="/contracts" class="view-all-btn">Ver todos</a>
                </div>
                <div class="table-content">
                    <div class="table-wrapper">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>Cliente</th>
                                    <th>Fecha Fin</th>
                                    <th>Días Rest.</th>
                                    <th>Acción</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($contratosProximos as $contrato)
                                <tr>
                                    <td>
                                        <div class="client-info">
                                            <div class="client-avatar">
                                                {{ substr($contrato['cliente'], 0, 1) }}
                                            </div>
                                            <span class="client-name">{{ $contrato['cliente'] }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="date">{{ $contrato['fecha_fin'] }}</span>
                                    </td>
                                    <td>
                                        <span class="days-remaining {{ $contrato['dias_restantes'] <= 7 ? 'urgent' : 'warning' }}">
                                            {{ $contrato['dias_restantes'] }} días
                                        </span>
                                    </td>
                                    <td>
                                        <button class="action-btn primary">Renovar</button>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('Chart.js cargado:', typeof Chart !== 'undefined');

    // Configuración global para Chart.js con tema oscuro mejorado
    Chart.defaults.color = '#cbd5e1';
    Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';
    Chart.defaults.backgroundColor = 'rgba(59, 130, 246, 0.1)';
    Chart.defaults.font = {
        family: 'Inter',
        size: 12,
        weight: '500'
    };

    // Datos de pagos por mes desde el controller
    const pagosPorMes = @json($pagosPorMes);
    console.log('Datos de pagos por mes:', pagosPorMes);

    // Crear array de datos para los 12 meses
    const mesesData = [
        pagosPorMes['Enero'] || 0,
        pagosPorMes['Febrero'] || 0,
        pagosPorMes['Marzo'] || 0,
        pagosPorMes['Abril'] || 0,
        pagosPorMes['Mayo'] || 0,
        pagosPorMes['Junio'] || 0,
        pagosPorMes['Julio'] || 0,
        pagosPorMes['Agosto'] || 0,
        pagosPorMes['Septiembre'] || 0,
        pagosPorMes['Octubre'] || 0,
        pagosPorMes['Noviembre'] || 0,
        pagosPorMes['Diciembre'] || 0
    ];

    console.log('Datos procesados:', mesesData);

    // Gráfico de Pagos Mensuales
    const ctxPagos = document.getElementById('pagosMensualesChart');
    if (ctxPagos) {

        const pagosMensualesChart = new Chart(ctxPagos, {
            type: 'line',
            data: {
                labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
                datasets: [{
                    label: 'Pagos Recibidos ($)',
                    data: mesesData,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 5,
                    pointHoverRadius: 8,
                    pointHoverBackgroundColor: '#3b82f6',
                    pointHoverBorderColor: '#ffffff',
                    pointHoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    intersect: false,
                    mode: 'index'
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top',
                        align: 'end',
                        labels: {
                            color: '#cbd5e1',
                            font: {
                                family: 'Inter',
                                size: 12,
                                weight: '500'
                            },
                            usePointStyle: true,
                            pointStyle: 'circle',
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.95)',
                        titleColor: '#f8fafc',
                        bodyColor: '#cbd5e1',
                        borderColor: '#334155',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return 'Pagos: $' + context.parsed.y.toLocaleString();
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)',
                            drawBorder: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#94a3b8',
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            callback: function(value) {
                                return '$' + value.toLocaleString();
                            },
                            padding: 10
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(255, 255, 255, 0.05)',
                            drawBorder: false
                        },
                        border: {
                            display: false
                        },
                        ticks: {
                            color: '#94a3b8',
                            font: {
                                family: 'Inter',
                                size: 11
                            },
                            padding: 10
                        }
                    }
                }
            }
        });
    }

    // Gráfico de Estado de Contratos
    const ctxContratos = document.getElementById('estadoContratosChart');
    if (ctxContratos) {
        const estadoContratosChart = new Chart(ctxContratos, {
            type: 'doughnut',
            data: {
                labels: ['Activos', 'Por Vencer', 'Vencidos'],
                datasets: [{
                    data: [
                        {{ $estadisticas['totalContratos'] * 0.7 }},
                        {{ $estadisticas['totalContratos'] * 0.2 }},
                        {{ $estadisticas['totalContratos'] * 0.1 }}
                    ],
                    backgroundColor: [
                        '#10b981',
                        '#f59e0b',
                        '#ef4444'
                    ],
                    borderColor: [
                        '#059669',
                        '#d97706',
                        '#dc2626'
                    ],
                    borderWidth: 0,
                    hoverOffset: 8,
                    cutout: '65%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(30, 41, 59, 0.95)',
                        titleColor: '#f8fafc',
                        bodyColor: '#cbd5e1',
                        borderColor: '#334155',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.parsed;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = ((value / total) * 100).toFixed(1);
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                },
                elements: {
                    arc: {
                        borderWidth: 0
                    }
                }
            }
        });
    }

    // Interactividad para botones de gráficos
    document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Remover clase active de todos los botones del mismo grupo
            this.parentElement.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            // Agregar clase active al botón clickeado
            this.classList.add('active');
        });
    });
});
</script>
@endsection


