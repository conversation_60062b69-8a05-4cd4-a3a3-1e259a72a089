<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{{ config('app.name') }} - Portal Empresarial de Consulta de Pagos</title>
    
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
            --gold-gradient: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
            --glass-bg: rgba(255, 255, 255, 0.03);
            --glass-border: rgba(255, 255, 255, 0.08);
            --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --card-bg: rgba(255, 255, 255, 0.05);
            --card-border: rgba(255, 255, 255, 0.1);
            --shadow-glow: 0 0 80px rgba(103, 126, 234, 0.3);
            --shadow-card: 0 30px 60px -12px rgba(0, 0, 0, 0.4);
            --shadow-input: 0 15px 35px -5px rgba(0, 0, 0, 0.2);
            --morphism-bg: rgba(255, 255, 255, 0.02);
            --morphism-border: rgba(255, 255, 255, 0.06);
            --morphism-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.3);
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.8);
            --text-muted: rgba(255, 255, 255, 0.6);
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Inter', system-ui, sans-serif;
            background: var(--primary-gradient);
            background-size: 400% 400%;
            animation: gradient 25s ease infinite;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem;
            color: var(--text-primary);
            position: relative;
            overflow-x: hidden;
            font-size: 12px;
            line-height: 1.4;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(ellipse at 20% 80%, rgba(102, 126, 234, 0.15) 0%, transparent 60%),
                radial-gradient(ellipse at 80% 20%, rgba(240, 147, 251, 0.12) 0%, transparent 60%),
                radial-gradient(ellipse at 40% 40%, rgba(79, 172, 254, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 60% 70%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
            z-index: -1;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 20s infinite ease-in-out;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation-delay: 5s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 15%;
            width: 60px;
            height: 60px;
            background: white;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation-delay: 10s;
        }

        .shape:nth-child(4) {
            bottom: 30%;
            right: 20%;
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 20px;
            animation-delay: 15s;
        }

        .form-container {
            background: var(--card-bg);
            backdrop-filter: blur(40px);
            -webkit-backdrop-filter: blur(40px);
            border: 1px solid var(--card-border);
            border-radius: 24px;
            padding: 2.5rem 2rem;
            box-shadow: 
                var(--morphism-shadow),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.05);
            width: 100%;
            max-width: 420px;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .form-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--gold-gradient);
            opacity: 0.6;
            border-radius: 40px 40px 0 0;
        }

        .form-container::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(180deg, rgba(255, 215, 0, 0.3), transparent);
            border-radius: 40px 0 0 40px;
        }

        .form-container:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 
                0 50px 100px -12px rgba(0, 0, 0, 0.4),
                0 0 0 1px rgba(255, 255, 255, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.2),
                0 0 120px rgba(102, 126, 234, 0.3);
        }

        .form-header {
            text-align: center;
            margin-bottom: 2.5rem;
        }

        .form-icon {
            width: 60px;
            height: 60px;
            background: var(--gold-gradient);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            box-shadow: 
                0 15px 30px -8px rgba(255, 215, 0, 0.3),
                0 0 0 1px rgba(255, 255, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: iconFloat 6s ease-in-out infinite;
        }

        .form-icon::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
            border-radius: 32px 32px 0 0;
        }

        .form-icon:hover {
            transform: translateY(-8px) scale(1.08) rotate(5deg);
            box-shadow: 
                0 35px 70px -12px rgba(255, 215, 0, 0.6),
                0 0 0 1px rgba(255, 255, 255, 0.3),
                0 0 80px rgba(255, 215, 0, 0.4);
        }

        @keyframes iconFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-10px) rotate(2deg); }
        }

        .form-title {
            font-size: 1.75rem;
            font-weight: 700;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffd700 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.75rem;
            letter-spacing: -0.02em;
            font-family: 'Playfair Display', serif;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            line-height: 1.2;
        }

        .form-subtitle {
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 400;
            line-height: 1.5;
            margin-bottom: 0.75rem;
        }

        .form-tagline {
            color: var(--text-muted);
            font-size: 0.75rem;
            font-weight: 500;
            text-align: center;
            padding: 0.75rem 1rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.05);
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .form-group {
            margin-bottom: 1.5rem;
        }

        .form-label {
            display: block;
            font-weight: 600;
            color: white;
            margin-bottom: 0.75rem;
            font-size: 0.8rem;
            letter-spacing: 0.025em;
            text-transform: uppercase;
        }

        .input-wrapper {
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: rgba(255, 255, 255, 0.6);
            pointer-events: none;
            transition: all 0.3s ease;
            z-index: 2;
        }

        .form-input {
            width: 100%;
            padding: 1rem 1rem 1rem 3rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            position: relative;
            letter-spacing: 0.025em;
            color: var(--text-primary);
            box-shadow: 
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
            font-weight: 400;
        }

        .form-input:focus {
            outline: none;
            border-color: rgba(255, 215, 0, 0.6);
            background: rgba(255, 255, 255, 0.08);
            box-shadow: 
                0 0 0 4px rgba(255, 215, 0, 0.15),
                0 12px 35px -5px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transform: translateY(-4px) scale(1.02);
        }

        .form-input:focus + .input-icon {
            color: #4facfe;
            transform: translateY(-50%) scale(1.1);
        }

        .form-input.is-invalid {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
            animation: shake 0.5s ease-in-out;
        }

        .form-input.is-invalid:focus {
            border-color: #ef4444;
            box-shadow: 
                0 0 0 4px rgba(239, 68, 68, 0.15),
                0 8px 25px -5px rgba(239, 68, 68, 0.2);
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .input-hint, .form-error-message {
            display: flex;
            align-items: center;
            margin-top: 0.5rem;
            font-size: 0.7rem;
            font-weight: 500;
        }

        .input-hint {
            color: rgba(255, 255, 255, 0.7);
            background: rgba(255, 255, 255, 0.05);
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            border-left: 2px solid rgba(255, 255, 255, 0.3);
        }

        .input-hint svg {
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        .form-error-message {
            color: #fca5a5;
            background: rgba(239, 68, 68, 0.1);
            padding: 0.5rem 0.75rem;
            border-radius: 8px;
            border-left: 2px solid #ef4444;
            display: none;
            animation: slideDown 0.3s ease-out;
        }

        .form-error-message svg {
            margin-right: 0.5rem;
            flex-shrink: 0;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .submit-btn {
            width: 100%;
            background: var(--gold-gradient);
            color: #1a1a2e;
            border: none;
            border-radius: 12px;
            padding: 1rem 1.5rem;
            font-size: 0.85rem;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            letter-spacing: 0.05em;
            text-transform: uppercase;
            box-shadow: 
                0 8px 20px -5px rgba(255, 215, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-family: 'Poppins', sans-serif;
        }

        .submit-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .submit-btn::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        }

        .submit-btn:hover::before {
            left: 100%;
        }

        .submit-btn:hover:not(:disabled) {
            transform: translateY(-6px) scale(1.05);
            box-shadow: 
                0 25px 50px -5px rgba(255, 215, 0, 0.6),
                0 0 0 1px rgba(255, 255, 255, 0.3),
                0 0 100px rgba(255, 215, 0, 0.4);
            background: linear-gradient(135deg, #ffed4e 0%, #ff9a56 50%, #ff6b6b 100%);
        }

        .submit-btn:active:not(:disabled) {
            transform: translateY(-1px) scale(0.98);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .loading-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .error-alert {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.2);
            border-left: 3px solid #ef4444;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: flex-start;
            box-shadow: 0 2px 8px -2px rgba(239, 68, 68, 0.1);
            animation: slideDown 0.4s ease-out;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        .error-icon {
            color: #fca5a5;
            margin-right: 0.75rem;
            flex-shrink: 0;
            margin-top: 0.125rem;
            background: rgba(239, 68, 68, 0.2);
            padding: 0.375rem;
            border-radius: 6px;
        }

        .error-content h3 {
            font-size: 0.8rem;
            font-weight: 700;
            color: #fca5a5;
            margin: 0 0 0.25rem 0;
        }

        .error-content p {
            font-size: 0.75rem;
            color: rgba(252, 165, 165, 0.9);
            margin: 0;
            line-height: 1.4;
        }

        /* Floating particles effect */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            animation: float-particle 15s infinite linear;
        }

        @keyframes float-particle {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 2s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 4s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 6s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 8s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 10s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 12s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 14s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 16s; }

        @media (max-width: 640px) {
            body {
                align-items: flex-start;
                padding: 0.75rem;
                padding-top: 1.5rem;
            }
            .form-container {
                padding: 2rem 1.5rem;
                border-radius: 20px;
            }
            .form-title {
                font-size: 1.5rem;
            }
            .form-subtitle {
                font-size: 0.8rem;
            }
            .form-icon {
                width: 48px;
                height: 48px;
                border-radius: 16px;
            }
            .form-input {
                padding: 0.875rem 0.875rem 0.875rem 2.5rem;
                font-size: 0.8rem;
            }
            .submit-btn {
                padding: 0.875rem 1.25rem;
                font-size: 0.75rem;
            }
        }

        @media (max-width: 480px) {
            .form-container {
                padding: 1.5rem 1.25rem;
                margin: 0.5rem;
            }
            .form-title {
                font-size: 1.25rem;
            }
            .form-subtitle {
                font-size: 0.75rem;
            }
        }

        .form-container {
            animation: fadeInUp 0.6s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Floating Particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>
    
    <div class="form-container">
        <div class="form-header">
            <div class="form-icon">
                <svg width="20" height="20" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2" class="text-white">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
            </div>
            <h1 class="form-title">Portal Empresarial</h1>
            <p class="form-subtitle">Consulta de Pagos Profesional</p>
            <div class="form-tagline">
                ✨ Acceso seguro y confiable a tu información financiera
            </div>
        </div>

        @if(session('error'))
        <div class="error-alert" role="alert">
            <div class="error-icon">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="error-content">
                <h3>Error en la consulta</h3>
                <p>{{ session('error') }}</p>
            </div>
        </div>
        @endif

        <form action="{{ route('public.search.results') }}" method="GET" id="searchForm" novalidate>
            <div class="form-group">
                <label for="cedula" class="form-label">Número de Cédula</label>
                <div class="input-wrapper">
                    <input 
                        type="text" 
                        name="cedula" 
                        id="cedula" 
                        required
                        class="form-input"
                        placeholder="Ingrese su número de cédula"
                        value="{{ old('cedula') }}"
                        pattern="[0-9]+"
                        title="Solo se permiten números."
                        maxlength="15"
                        inputmode="numeric"
                        aria-describedby="cedula-hint cedula-error"
                    >
                    <div class="input-icon">
                        <svg width="16" height="16" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                             <path stroke-linecap="round" stroke-linejoin="round" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                        </svg>
                    </div>
                </div>
                <div id="cedula-error" class="form-error-message" aria-live="polite">
                    <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div id="cedula-hint" class="input-hint">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                        <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                        <path d="m8.93 6.588-2.29.287-.082.38.45.083c.294.07.352.176.288.469l-.738 3.468c-.194.897.105 1.319.808 1.319.545 0 1.178-.252 1.465-.598l.088-.416c-.2.176-.492.246-.686.246-.275 0-.375-.193-.304-.533L8.93 6.588zM9 4.5a1 1 0 1 1-2 0 1 1 0 0 1 2 0z"/>
                    </svg>
                    <span>Sistema empresarial seguro - Solo números, sin puntos ni comas</span>
                </div>
            </div>

            <button type="submit" class="submit-btn" id="submitBtn">
                <span class="btn-text">Acceder al Portal</span>
                <div class="loading-spinner" style="display: none;"></div>
            </button>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('searchForm');
            const input = document.getElementById('cedula');
            const submitBtn = document.getElementById('submitBtn');
            const btnText = submitBtn.querySelector('.btn-text');
            const spinner = submitBtn.querySelector('.loading-spinner');
            const errorContainer = document.getElementById('cedula-error');

            const MIN_CEDULA_LENGTH = 7;

            const setLoading = (isLoading) => {
                if (isLoading) {
                    submitBtn.disabled = true;
                    btnText.textContent = 'Procesando...';
                    spinner.style.display = 'inline-block';
                } else {
                    submitBtn.disabled = false;
                    btnText.textContent = 'Acceder al Portal';
                    spinner.style.display = 'none';
                }
            };

            const showError = (message) => {
                errorContainer.innerHTML = `
                    <svg width="16" height="16" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                    <span>${message}</span>
                `;
                errorContainer.style.display = 'flex';
                input.classList.add('is-invalid');
                input.setAttribute('aria-invalid', 'true');
            };
            
            const clearError = () => {
                errorContainer.textContent = '';
                errorContainer.style.display = 'none';
                input.classList.remove('is-invalid');
                input.removeAttribute('aria-invalid');
            };

            input.addEventListener('input', () => {
                input.value = input.value.replace(/[^0-9]/g, '');
                if (input.classList.contains('is-invalid')) {
                    clearError();
                }
            });

            form.addEventListener('submit', (e) => {
                e.preventDefault();
                clearError();
                
                const cedulaValue = input.value.trim();

                if (cedulaValue.length < MIN_CEDULA_LENGTH) {
                    showError(`La cédula debe tener al menos ${MIN_CEDULA_LENGTH} dígitos.`);
                    input.focus();
                    return;
                }

                setLoading(true);

                setTimeout(() => {
                    if (submitBtn.disabled) {
                       setLoading(false);
                    }
                }, 10000);

                form.submit();
            });

            setTimeout(() => {
                input.focus();
            }, 300);
        });
    </script>
</body>
</html>