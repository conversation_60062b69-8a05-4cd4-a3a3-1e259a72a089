<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }} - Dashboard Ejecutivo de Resultados</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f0f9ff',
                            100: '#e0f2fe',
                            200: '#bae6fd',
                            300: '#7dd3fc',
                            400: '#38bdf8',
                            500: '#0ea5e9',
                            600: '#0284c7',
                            700: '#0369a1',
                            800: '#075985',
                            900: '#0c4a6e',
                        },
                        accent: {
                            50: '#fdf4ff',
                            100: '#fae8ff',
                            200: '#f5d0fe',
                            300: '#f0abfc',
                            400: '#e879f9',
                            500: '#d946ef',
                            600: '#c026d3',
                            700: '#a21caf',
                            800: '#86198f',
                            900: '#701a75',
                        }
                    },
                    animation: {
                        'gradient': 'gradient 8s linear infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 3s infinite',
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
            --luxury-gradient: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
            --platinum-gradient: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 50%, #ffffff 100%);
            --glass-bg: rgba(255, 255, 255, 0.02);
            --glass-border: rgba(255, 255, 255, 0.06);
            --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --card-bg: rgba(255, 255, 255, 0.04);
            --card-border: rgba(255, 255, 255, 0.08);
            --shadow-glow: 0 0 80px rgba(103, 126, 234, 0.2);
            --shadow-card: 0 30px 60px -12px rgba(0, 0, 0, 0.4);
            --shadow-input: 0 15px 35px -5px rgba(0, 0, 0, 0.2);
            --morphism-bg: rgba(255, 255, 255, 0.03);
            --morphism-border: rgba(255, 255, 255, 0.08);
            --morphism-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.3);
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.85);
            --text-muted: rgba(255, 255, 255, 0.65);
            --text-accent: #ffd700;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', system-ui, sans-serif;
            background: var(--primary-gradient);
            background-size: 400% 400%;
            animation: gradient 20s ease infinite;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(ellipse at 20% 80%, rgba(102, 126, 234, 0.12) 0%, transparent 60%),
                radial-gradient(ellipse at 80% 20%, rgba(240, 147, 251, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 40% 40%, rgba(79, 172, 254, 0.06) 0%, transparent 60%),
                radial-gradient(ellipse at 60% 70%, rgba(255, 215, 0, 0.04) 0%, transparent 50%);
            z-index: -1;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.015'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(5deg); }
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: var(--shadow-glow); }
            50% { box-shadow: 0 0 60px rgba(59, 130, 246, 0.5); }
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: var(--primary-gradient);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 20s infinite ease-in-out;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation-delay: 5s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 15%;
            width: 60px;
            height: 60px;
            background: white;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation-delay: 10s;
        }

        .shape:nth-child(4) {
            bottom: 30%;
            right: 20%;
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 20px;
            animation-delay: 15s;
        }

        .glass-card {
            background: var(--card-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--card-border);
            box-shadow: var(--morphism-shadow);
            border-radius: 28px;
            position: relative;
            overflow: hidden;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }

        .glass-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 1px;
            height: 100%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.2), transparent);
        }

        .morphism-button {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 
                0 8px 32px 0 rgba(31, 38, 135, 0.37),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 16px;
            color: white;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
        }

        .morphism-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 
                0 12px 40px 0 rgba(31, 38, 135, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
        }

        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-completado {
            background: rgba(34, 197, 94, 0.2);
            color: #16a34a;
            border-color: rgba(34, 197, 94, 0.3);
        }

        .status-activo {
            background: rgba(59, 130, 246, 0.2);
            color: #2563eb;
            border-color: rgba(59, 130, 246, 0.3);
        }

        .status-vencido {
            background: rgba(239, 68, 68, 0.2);
            color: #dc2626;
            border-color: rgba(239, 68, 68, 0.3);
        }

        .info-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            padding: 1.5rem;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            position: relative;
            overflow: hidden;
        }

        .info-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }

        .table-glass {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(15px);
            -webkit-backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            overflow: hidden;
        }

        .table-glass th {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-weight: 600;
            padding: 1rem;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table-glass td {
            padding: 1rem;
            color: white;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .table-glass tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        /* Floating particles effect */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float-particle 15s infinite linear;
        }

        @keyframes float-particle {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 2s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 4s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 6s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 8s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 10s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 12s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 14s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 16s; }

        @media (max-width: 768px) {
            .glass-card {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }
        }
    </style>
</head>
<body class="min-h-screen antialiased">
    <!-- Animated Background -->
    <div class="animated-bg"></div>
    
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Floating Particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <div class="min-h-screen py-12 px-4">
        <div class="max-w-7xl mx-auto">
            <!-- Header Navigation -->
            <div class="mb-12">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6">
                    <a href="{{ route('public.search') }}" class="morphism-button group">
                        <svg class="w-5 h-5 mr-3 transition-transform group-hover:-translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                        </svg>
                        Nueva Consulta
                    </a>
                    
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-white/60 text-sm">
                            <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                            <span>Sistema en línea</span>
                        </div>

                    </div>
                </div>
            </div>

            <!-- Client Information -->
            <div class="glass-card p-10 mb-12">
                <div class="flex items-center space-x-6 mb-8">
                    <div class="w-20 h-20 bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600 rounded-3xl flex items-center justify-center border-2 border-white/20 shadow-2xl">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <div>
                        <div class="flex items-center space-x-3 mb-2">
                            <h2 class="text-4xl font-bold text-white font-display">
                                Perfil del Cliente
                            </h2>
                            <div class="px-3 py-1 bg-green-500/20 border border-green-500/30 rounded-full text-green-300 text-xs font-medium">
                                Verificado
                            </div>
                        </div>
                        <p class="text-white/70 text-lg">Información registrada y certificada en el sistema</p>
                    </div>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <div class="info-card group">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs text-white/60 uppercase tracking-wide font-medium">Nombre Completo</p>
                                <p class="font-bold text-white text-lg leading-tight">{{ $client->nombre_completo }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="info-card group">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs text-white/60 uppercase tracking-wide font-medium">Cédula</p>
                                <p class="font-bold text-white text-lg">{{ $client->cedula }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="info-card group">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs text-white/60 uppercase tracking-wide font-medium">Teléfono</p>
                                <p class="font-bold text-white text-lg">{{ $client->telefono ?? 'No registrado' }}</p>
                            </div>
                        </div>
                    </div>

                    <div class="info-card group">
                        <div class="flex items-center space-x-4 mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center shadow-lg">
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                </svg>
                            </div>
                            <div>
                                <p class="text-xs text-white/60 uppercase tracking-wide font-medium">Email</p>
                                <p class="font-bold text-white text-lg break-all">{{ $client->email ?? 'No registrado' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contracts and Payments -->
            <div class="glass-card p-10">
                <div class="flex items-center justify-between mb-10">
                    <div class="flex items-center space-x-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-3xl flex items-center justify-center shadow-2xl">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-3xl font-bold text-white font-display">Portfolio de Contratos</h3>
                            <p class="text-white/70 text-lg">Gestión integral de pagos y obligaciones</p>
                        </div>
                    </div>
                    
                    <div class="text-right">
                        <div class="text-sm text-white/60 mb-1">Total de Contratos</div>
                        <div class="text-3xl font-bold text-white">{{ $client->contracts->count() }}</div>
                    </div>
                </div>

                <div class="space-y-8">
                    @forelse($client->contracts as $contract)
                        <div class="glass-card p-6">
                            <div class="flex justify-between items-start mb-6">
                                <div>
                                    <h4 class="text-xl font-semibold text-white flex items-center mb-2">
                                        Contrato #{{ $contract->id }}
                                        <span class="status-badge ml-3 {{ $contract->estado === 'completado' ? 'status-completado' : ($contract->estado === 'activo' ? 'status-activo' : 'status-vencido') }}">
                                            {{ ucfirst($contract->estado) }}
                                        </span>
                                    </h4>
                                    <p class="text-white/70">Fecha de inicio: {{ $contract->fecha_inicio->format('d/m/Y') }}</p>
                                </div>
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                                <div class="info-card">
                                    <p class="text-sm text-white/70 mb-1">Monto Total</p>
                                    <p class="text-2xl font-bold text-white">${{ number_format($contract->monto_total, 2) }}</p>
                                </div>
                                <div class="info-card">
                                    <p class="text-sm text-white/70 mb-1">Cuotas Pagadas</p>
                                    <p class="text-2xl font-bold text-white">{{ $contract->cuotas_pagadas }} de {{ $contract->numero_cuotas }}</p>
                                </div>
                                <div class="info-card">
                                    <p class="text-sm text-white/70 mb-1">Monto por Cuota</p>
                                    <p class="text-2xl font-bold text-white">${{ number_format($contract->monto_cuota, 2) }}</p>
                                </div>
                            </div>

                            @if($contract->payments->isNotEmpty())
                                <div class="mt-6">
                                    <h5 class="font-semibold mb-4 text-white text-lg flex items-center">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                        Historial de Pagos
                                    </h5>
                                    <div class="table-glass">
                                        <table class="min-w-full">
                                            <thead>
                                                <tr>
                                                    <th>Cuota</th>
                                                    <th>Fecha</th>
                                                    <th>Monto</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($contract->payments as $payment)
                                                    <tr>
                                                        <td>{{ $payment->numero_cuota }}</td>
                                                        <td>{{ $payment->fecha_pago->format('d/m/Y') }}</td>
                                                        <td class="font-semibold">
                                                            ${{ number_format($payment->monto, 2) }}
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            @else
                                <div class="info-card text-center">
                                    <p class="text-white/70 italic">No hay pagos registrados para este contrato.</p>
                                </div>
                            @endif
                        </div>
                    @empty
                        <div class="info-card text-center">
                            <p class="text-white/70 italic">No hay contratos registrados para este cliente.</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</body>
</html>