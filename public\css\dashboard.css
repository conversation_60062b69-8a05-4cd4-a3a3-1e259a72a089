/* Dashboard Styles - Improved Design */
:root {
    --primary-color: #3b82f6;
    --primary-dark: #2563eb;
    --secondary-color: #1e293b;
    --success-color: #10b981;
    --success-dark: #059669;
    --warning-color: #f59e0b;
    --warning-dark: #d97706;
    --danger-color: #ef4444;
    --danger-dark: #dc2626;
    --info-color: #06b6d4;
    --info-dark: #0891b2;
    --dark-bg: #0f172a;
    --darker-bg: #020617;
    --card-bg: #1e293b;
    --card-hover: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #94a3b8;
    --border-color: #334155;
    --border-light: #475569;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: linear-gradient(135deg, var(--darker-bg) 0%, var(--dark-bg) 50%, var(--secondary-color) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* Dashboard Main Content */
.dashboard-main-content {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

/* Dashboard Header */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.header-content h1.dashboard-title {
    font-size: 2.5rem;
    font-weight: 800;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    font-weight: 400;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.date-info {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(59, 130, 246, 0.1);
    padding: 0.75rem 1rem;
    border-radius: 12px;
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: var(--primary-color);
    font-weight: 500;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2.5rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(30, 41, 59, 0.8) 100%);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--info-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-light);
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card.primary::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-dark));
}

.stat-card.success::before {
    background: linear-gradient(90deg, var(--success-color), var(--success-dark));
}

.stat-card.warning::before {
    background: linear-gradient(90deg, var(--warning-color), var(--warning-dark));
}

.stat-card.info::before {
    background: linear-gradient(90deg, var(--info-color), var(--info-dark));
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    position: relative;
    overflow: hidden;
    flex-shrink: 0;
}

.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
    color: white;
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), var(--warning-dark));
    color: white;
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, var(--info-color), var(--info-dark));
    color: white;
}

.stat-info {
    flex: 1;
}

.stat-number {
    font-size: 2.25rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
    margin-bottom: 0.75rem;
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
}

.stat-trend.positive {
    color: var(--success-color);
}

.stat-trend.negative {
    color: var(--danger-color);
}

.stat-trend i {
    font-size: 0.625rem;
}

/* Financial Summary */
.financial-summary {
    margin-bottom: 2.5rem;
}

.summary-card {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(30, 41, 59, 0.8) 100%);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
}

.summary-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.summary-header h3 {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--text-primary);
}

.period-selector {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.5rem 1rem;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.summary-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.metric {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(15, 23, 42, 0.3);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    flex-shrink: 0;
}

.metric-icon.success {
    background: linear-gradient(135deg, var(--success-color), var(--success-dark));
    color: white;
}

.metric-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.metric-icon.danger {
    background: linear-gradient(135deg, var(--danger-color), var(--danger-dark));
    color: white;
}

.metric-data {
    flex: 1;
}

.metric-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.metric-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

/* Charts Section */
.charts-section {
    margin-bottom: 2.5rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(30, 41, 59, 0.8) 100%);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 1.5rem;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.chart-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-light);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.chart-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

.chart-title i {
    color: var(--primary-color);
}

.chart-actions {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 0.375rem 0.75rem;
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.chart-btn:hover,
.chart-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.chart-legend {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.legend-color.active {
    background: var(--success-color);
}

.legend-color.warning {
    background: var(--warning-color);
}

.legend-color.danger {
    background: var(--danger-color);
}

.chart-container {
    height: 300px;
    position: relative;
}

/* Data Tables */
.data-tables {
    margin-bottom: 2rem;
}

.tables-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 1.5rem;
}

.table-card {
    background: linear-gradient(135deg, var(--card-bg) 0%, rgba(30, 41, 59, 0.8) 100%);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.table-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-light);
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.table-title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--text-primary);
}

.table-title i {
    color: var(--primary-color);
}

.view-all-btn {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.view-all-btn:hover {
    background: rgba(59, 130, 246, 0.1);
}

.table-content {
    padding: 0;
}

.table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background: rgba(15, 23, 42, 0.5);
    padding: 1rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--border-color);
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    vertical-align: middle;
}

.data-table tr:hover {
    background: rgba(59, 130, 246, 0.05);
}

.client-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.client-avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--info-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.client-name {
    font-weight: 500;
    color: var(--text-primary);
}

.amount {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.date {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
}

.status-badge.success {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.days-remaining {
    font-weight: 600;
    font-size: 0.875rem;
}

.days-remaining.warning {
    color: var(--warning-color);
}

.days-remaining.urgent {
    color: var(--danger-color);
}

.action-btn {
    background: var(--primary-color);
    color: white;
    border: none;
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
}

.action-btn.primary {
    background: var(--primary-color);
}

.action-btn.primary:hover {
    background: var(--primary-dark);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .dashboard-main-content {
        padding: 1.5rem;
    }

    .dashboard-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .dashboard-stats {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .charts-grid,
    .tables-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .dashboard-main-content {
        padding: 1rem;
    }

    .header-content h1.dashboard-title {
        font-size: 2rem;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .summary-metrics {
        grid-template-columns: 1fr;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .table-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
}

/* Animations and Transitions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.dashboard-stats .stat-card {
    animation: fadeInUp 0.6s ease-out;
}

.dashboard-stats .stat-card:nth-child(1) { animation-delay: 0.1s; }
.dashboard-stats .stat-card:nth-child(2) { animation-delay: 0.2s; }
.dashboard-stats .stat-card:nth-child(3) { animation-delay: 0.3s; }
.dashboard-stats .stat-card:nth-child(4) { animation-delay: 0.4s; }

.chart-card,
.table-card {
    animation: fadeInUp 0.6s ease-out;
    animation-delay: 0.5s;
    animation-fill-mode: both;
}

/* Loading States */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--darker-bg);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
    transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-light);
}

/* Focus States */
button:focus,
select:focus,
input:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .dashboard-main-content {
        padding: 0;
        max-width: none;
    }

    .chart-card,
    .table-card {
        break-inside: avoid;
        box-shadow: none;
        border: 1px solid #ccc;
    }

    .chart-container {
        height: 200px !important;
    }
}
