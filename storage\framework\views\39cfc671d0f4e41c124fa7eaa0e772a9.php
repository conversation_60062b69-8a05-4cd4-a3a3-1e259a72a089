<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo e(config('app.name', 'Laravel')); ?> - Dashboard</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Plus+Jakarta+Sans:wght@500;600;700;800&display=swap" rel="stylesheet">
    <!-- Incluimos Font Awesome para íconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Dashboard CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/dashboard.css')); ?>">
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>
    <!-- Dashboard JavaScript -->
    <script src="<?php echo e(asset('js/dashboard.js')); ?>" defer></script>
    <style>
        :root {
            /* Professional Dark Dashboard Theme */
            --primary-dark: #0a0a0a;
            --secondary-dark: #1a1a1a;
            --accent-dark: #2a2a2a;
            --border-dark: #333333;

            /* Professional Color System */
            --primary: #3b82f6;
            --primary-hover: #2563eb;
            --primary-light: #60a5fa;
            --accent: #1d4ed8;
            --success: #10b981;
            --warning: #f59e0b;
            --danger: #ef4444;
            --info: #06b6d4;

            /* Glass Morphism - Professional Black */
            --glass-bg: rgba(10, 10, 10, 0.8);
            --glass-bg-light: rgba(26, 26, 26, 0.7);
            --glass-bg-sidebar: rgba(15, 15, 15, 0.95);
            --glass-border: rgba(255, 255, 255, 0.1);
            --glass-border-light: rgba(255, 255, 255, 0.15);
            --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.9);

            /* Professional Backgrounds */
            --bg-gradient: linear-gradient(135deg, #000000 0%, #1a1a1a 25%, #0f0f0f 50%, #1a1a1a 75%, #000000 100%);
            --sidebar-gradient: linear-gradient(180deg, rgba(15, 15, 15, 0.95) 0%, rgba(10, 10, 10, 0.98) 100%);
            --card-gradient: linear-gradient(145deg, rgba(26, 26, 26, 0.8) 0%, rgba(10, 10, 10, 0.9) 100%);

            /* Professional Text Colors */
            --text-primary: #ffffff;
            --text-secondary: #e5e5e5;
            --text-muted: #a3a3a3;
            --text-accent: #60a5fa;

            /* Layout Variables */
            --sidebar-width: 280px;
            --sidebar-collapsed-width: 80px;
            --topbar-height: 70px;
            
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            
            --transition-default: all 0.3s ease;
            --transition-fast: all 0.15s ease;
            --transition-slow: all 0.5s ease;
            
            --radius-sm: 0.25rem;
            --radius: 0.5rem;
            --radius-md: 0.75rem;
            --radius-lg: 1rem;
            --radius-xl: 1.5rem;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: var(--bg-gradient);
            background-attachment: fixed;
            color: var(--text-primary);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            min-height: 100vh;
            position: relative;
        }

        /* Professional background overlay */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(29, 78, 216, 0.03) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(37, 99, 235, 0.02) 0%, transparent 50%);
            animation: backgroundShift 30s ease-in-out infinite;
            pointer-events: none;
            z-index: -1;
        }

        @keyframes backgroundShift {
            0%, 100% { transform: scale(1) rotate(0deg); opacity: 0.5; }
            50% { transform: scale(1.05) rotate(1deg); opacity: 0.8; }
        }
        
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Plus Jakarta Sans', sans-serif;
            font-weight: 700;
            color: var(--text-primary);
        }
        
        a {
            text-decoration: none;
            color: inherit;
        }
        
        /* Animations */
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideInLeft {
            from { transform: translateX(-20px); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        /* Layout */
        .dashboard-container {
            display: grid;
            grid-template-columns: var(--sidebar-width) 1fr;
            min-height: 100vh;
            transition: var(--transition-default);
        }
        
        .dashboard-container.collapsed {
            grid-template-columns: var(--sidebar-collapsed-width) 1fr;
        }
        
        /* Professional Sidebar with Dark Glass Morphism */
        .sidebar {
            background: var(--glass-bg-sidebar);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid var(--glass-border);
            color: var(--text-primary);
            position: fixed;
            width: var(--sidebar-width);
            height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
            transition: var(--transition-default);
            z-index: 50;
            box-shadow: var(--glass-shadow);
        }
        
        .dashboard-container.collapsed .sidebar {
            width: var(--sidebar-collapsed-width);
        }
        
        .sidebar-header {
            padding: 1.25rem 1.5rem;
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: var(--topbar-height);
            background: var(--glass-bg-light);
        }

        .logo-container {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            overflow: hidden;
        }

        .logo-icon {
            width: 36px;
            height: 36px;
            background: var(--primary);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            font-weight: 700;
            flex-shrink: 0;
            color: white;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
        
        .logo-text {
            font-family: 'Inter', sans-serif;
            font-weight: 600;
            font-size: 1.125rem;
            white-space: nowrap;
            transition: var(--transition-default);
            color: var(--text-primary);
            letter-spacing: -0.01em;
        }

        .dashboard-container.collapsed .logo-text {
            opacity: 0;
            width: 0;
        }

        .toggle-sidebar {
            background: var(--glass-bg-light);
            border: 1px solid var(--glass-border);
            color: var(--text-secondary);
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-default);
        }

        .toggle-sidebar:hover {
            background: var(--glass-border-light);
            color: var(--text-primary);
        }
        
        .toggle-sidebar i {
            transition: var(--transition-default);
        }

        .dashboard-container.collapsed .toggle-sidebar i {
            transform: rotate(180deg);
        }

        /* Professional Navigation */
        .sidebar-nav {
            padding: 1rem 0;
        }

        .nav-section {
            margin-bottom: 1.25rem;
        }

        .nav-section-title {
            padding: 0 1.5rem;
            font-size: 0.7rem;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            color: var(--text-muted);
            margin-bottom: 0.5rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: var(--transition-default);
            font-weight: 600;
        }

        .dashboard-container.collapsed .nav-section-title {
            opacity: 0;
            height: 0;
            margin: 0;
            padding: 0;
        }
        
        .nav-item {
            padding: 0.625rem 1.25rem;
            margin: 0.125rem 0.75rem;
            border-radius: 8px;
            transition: var(--transition-default);
            display: flex;
            align-items: center;
            gap: 0.875rem;
            cursor: pointer;
            position: relative;
            overflow: hidden;
            color: var(--text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }

        .nav-item:hover {
            background: var(--glass-bg-light);
            color: var(--text-primary);
            transform: translateX(2px);
        }

        .nav-item.active {
            background: var(--primary);
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }

        .nav-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            height: 100%;
            width: 3px;
            background: white;
            border-radius: 0 2px 2px 0;
        }
        
        .nav-icon {
            width: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }
        
        .nav-text {
            white-space: nowrap;
            transition: var(--transition-default);
        }
        
        .dashboard-container.collapsed .nav-text {
            opacity: 0;
            width: 0;
        }
        
        .nav-badge {
            background: var(--danger);
            color: white;
            font-size: 0.75rem;
            padding: 0.125rem 0.5rem;
            border-radius: var(--radius-full);
            margin-left: auto;
            transition: var(--transition-default);
        }
        
        .dashboard-container.collapsed .nav-badge {
            opacity: 0;
            width: 0;
            margin: 0;
        }
        
        /* Tooltip for collapsed sidebar */
        .nav-tooltip {
            position: absolute;
            left: 100%;
            top: 50%;
            transform: translateY(-50%);
            background: var(--dark);
            color: white;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius);
            font-size: 0.875rem;
            white-space: nowrap;
            opacity: 0;
            pointer-events: none;
            transition: var(--transition-fast);
            z-index: 10;
            box-shadow: var(--shadow-lg);
        }
        
        .dashboard-container.collapsed .nav-item:hover .nav-tooltip {
            opacity: 1;
            transform: translateY(-50%) translateX(10px);
        }
        
        /* Professional Sidebar Footer */
        .sidebar-footer {
            padding: 1rem 1.25rem;
            border-top: 1px solid var(--glass-border);
            margin-top: auto;
            background: var(--glass-bg-light);
        }
        
        .sidebar-footer-content {
            background: var(--glass-bg-light);
            border: 1px solid var(--glass-border);
            border-radius: 10px;
            padding: 0.875rem;
            transition: var(--transition-default);
        }

        .dashboard-container.collapsed .sidebar-footer-content {
            padding: 0.875rem 0.5rem;
        }

        .sidebar-footer-title {
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 0.375rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: var(--transition-default);
            color: var(--text-primary);
        }

        .dashboard-container.collapsed .sidebar-footer-title {
            opacity: 0;
            height: 0;
            margin: 0;
        }

        .sidebar-footer-text {
            font-size: 0.7rem;
            color: var(--text-muted);
            margin-bottom: 0.75rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            transition: var(--transition-default);
        }
        
        .dashboard-container.collapsed .sidebar-footer-text {
            opacity: 0;
            height: 0;
            margin: 0;
        }
        
        .sidebar-footer-button {
            background: var(--primary);
            color: white;
            border: 1px solid var(--primary-hover);
            border-radius: 8px;
            padding: 0.5rem 0.875rem;
            font-size: 0.8rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: var(--transition-default);
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .sidebar-footer-button:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        
        .dashboard-container.collapsed .sidebar-footer-button span {
            display: none;
        }
        
        /* Professional Main Content */
        .main-content {
            margin-left: var(--sidebar-width);
            transition: var(--transition-default);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--bg-gradient);
            position: relative;
        }

        .main-content::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(29, 78, 216, 0.05) 0%, transparent 50%);
            pointer-events: none;
        }

        .dashboard-container.collapsed .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }
        
        /* Professional Top Bar with Glass Morphism */
        .top-bar {
            height: var(--topbar-height);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 2rem;
            position: sticky;
            top: 0;
            z-index: 40;
            box-shadow: var(--glass-shadow);
        }
        
        .search-bar {
            position: relative;
            width: 280px;
        }

        .search-input {
            width: 100%;
            padding: 0.625rem 1rem 0.625rem 2.5rem;
            border: 1px solid var(--glass-border);
            border-radius: 8px;
            font-size: 0.875rem;
            transition: var(--transition-default);
            background: var(--glass-bg-light);
            color: var(--text-primary);
            backdrop-filter: blur(10px);
        }

        .search-input::placeholder {
            color: var(--text-muted);
        }

        .search-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
            background: var(--glass-bg);
        }
        
        .search-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            pointer-events: none;
        }

        .top-bar-actions {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .action-button {
            background: var(--glass-bg-light);
            border: 1px solid var(--glass-border);
            color: var(--text-secondary);
            width: 36px;
            height: 36px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-default);
            position: relative;
            backdrop-filter: blur(10px);
        }

        .action-button:hover {
            background: var(--glass-border-light);
            color: var(--text-primary);
            transform: translateY(-1px);
        }
        
        .notification-badge {
            position: absolute;
            top: 0;
            right: 0;
            background: var(--danger);
            color: white;
            font-size: 0.625rem;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2px solid white;
        }
        
        /* Profile Menu */
        .profile-menu {
            position: relative;
        }
        
        .profile-button {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.5rem;
            border-radius: var(--radius);
            cursor: pointer;
            transition: var(--transition-default);
        }
        
        .profile-button:hover {
            background: var(--gray-100);
        }
        
        .profile-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-100);
            color: var(--primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 1rem;
        }
        
        .profile-info {
            display: flex;
            flex-direction: column;
        }
        
        .profile-name {
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--gray-900);
        }
        
        .profile-role {
            font-size: 0.75rem;
            color: var(--gray-500);
        }
        
        .profile-dropdown {
            position: absolute;
            right: 0;
            top: calc(100% + 0.5rem);
            background: white;
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            min-width: 240px;
            padding: 0.5rem;
            z-index: 50;
            border: 1px solid var(--gray-200);
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: var(--transition-default);
        }
        
        .profile-menu.active .profile-dropdown {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        
        .dropdown-header {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid var(--gray-200);
            margin-bottom: 0.5rem;
        }
        
        .dropdown-user {
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--gray-900);
        }
        
        .dropdown-email {
            font-size: 0.75rem;
            color: var(--gray-500);
            margin-top: 0.25rem;
        }
        
        .dropdown-item {
            padding: 0.75rem 1rem;
            border-radius: var(--radius);
            transition: var(--transition-default);
            display: flex;
            align-items: center;
            gap: 0.75rem;
            font-size: 0.875rem;
        }
        
        .dropdown-item:hover {
            background: var(--gray-100);
        }
        
        .dropdown-icon {
            width: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-500);
        }
        
        .dropdown-divider {
            height: 1px;
            background: var(--gray-200);
            margin: 0.5rem 0;
        }
        
        .dropdown-item.danger {
            color: var(--danger);
        }
        
        .dropdown-item.danger .dropdown-icon {
            color: var(--danger);
        }
        
        .dropdown-item.danger:hover {
            background: #fee2e2;
        }
        
        /* Professional Page Content with Dark Glass */
        .page-content {
            padding: 1.75rem;
            flex: 1;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            margin: 1rem;
            border-radius: 16px;
            border: 1px solid var(--glass-border);
            position: relative;
            z-index: 1;
            min-height: calc(100vh - 140px);
            overflow: visible;
        }

        .page-header {
            margin-bottom: 1.75rem;
        }

        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.375rem;
            color: var(--text-primary);
            letter-spacing: -0.01em;
        }

        .page-description {
            color: var(--text-muted);
            font-size: 0.875rem;
            font-weight: 400;
        }
        
        /* Professional Dashboard Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
            gap: 1.25rem;
            margin-bottom: 1.75rem;
        }

        .stat-card {
            background: var(--card-gradient);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-radius: 12px;
            padding: 1.25rem;
            box-shadow: var(--glass-shadow);
            transition: var(--transition-default);
            border: 1px solid var(--glass-border);
            position: relative;
            overflow: hidden;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            border-color: var(--glass-border-light);
            box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.8);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, var(--primary) 0%, var(--primary-light) 100%);
            border-radius: 12px 12px 0 0;
        }

        .stat-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: 1rem;
        }

        .stat-icon {
            width: 44px;
            height: 44px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.125rem;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
        }

        .stat-icon.blue {
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
        }

        .stat-icon.green {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .stat-icon.purple {
            background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
        }

        .stat-icon.indigo {
            background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
        }

        .stat-icon.red {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }

        .stat-content {
            margin-top: 0.75rem;
        }

        .stat-label {
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 0.375rem;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-value {
            font-size: 1.875rem;
            font-weight: 700;
            line-height: 1;
            color: var(--text-primary);
            font-family: 'Inter', sans-serif;
        }

        /* Chart Containers */
        .chart-container {
            position: relative;
            padding: 1rem;
            background: var(--glass-bg-light);
            border-radius: 10px;
            border: 1px solid var(--glass-border);
        }

        .chart-container canvas {
            background: transparent !important;
        }

        /* Professional Buttons */
        .btn-primary {
            background: var(--primary);
            color: white;
            border: 1px solid var(--primary-hover);
            border-radius: 8px;
            padding: 0.625rem 1.25rem;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            transition: var(--transition-default);
            text-decoration: none;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }

        .btn-primary:hover {
            background: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }

        .btn-action {
            background: var(--glass-bg-light);
            color: var(--text-secondary);
            border: 1px solid var(--glass-border);
            border-radius: 6px;
            padding: 0.375rem 0.75rem;
            font-size: 0.75rem;
            font-weight: 500;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            gap: 0.375rem;
            transition: var(--transition-default);
            text-decoration: none;
        }

        .btn-action:hover {
            background: var(--glass-bg);
            border-color: var(--glass-border-light);
            transform: translateY(-1px);
        }

        .btn-view:hover {
            color: var(--primary);
            border-color: var(--primary);
        }

        .btn-edit:hover {
            color: var(--warning);
            border-color: var(--warning);
        }

        .btn-delete:hover {
            color: var(--danger);
            border-color: var(--danger);
        }

        /* Professional Alerts */
        .alert {
            padding: 1rem 1.25rem;
            border-radius: 8px;
            border: 1px solid;
            display: flex;
            align-items: center;
            font-size: 0.875rem;
            font-weight: 500;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.3);
            color: #10b981;
        }

        .stat-icon.indigo {
            background: linear-gradient(135deg, rgba(79, 70, 229, 0.2), rgba(79, 70, 229, 0.1));
            color: #4f46e5;
            border: 1px solid rgba(79, 70, 229, 0.2);
        }

        .stat-icon.orange {
            background: linear-gradient(135deg, rgba(245, 158, 11, 0.2), rgba(245, 158, 11, 0.1));
            color: #f59e0b;
            border: 1px solid rgba(245, 158, 11, 0.2);
        }

        .stat-icon.red {
            background: linear-gradient(135deg, rgba(239, 68, 68, 0.2), rgba(239, 68, 68, 0.1));
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }
        
        .stat-menu {
            color: var(--gray-400);
            cursor: pointer;
            transition: var(--transition-default);
        }
        
        .stat-menu:hover {
            color: var(--gray-700);
        }
        
        .stat-value {
            font-size: 1.75rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .stat-label {
            color: var(--gray-500);
            font-size: 0.875rem;
        }
        
        .stat-change {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin-top: 1rem;
            font-size: 0.875rem;
        }
        
        .stat-change.positive {
            color: var(--success);
        }
        
        .stat-change.negative {
            color: var(--danger);
        }
        
        /* Recent Activity */
        .activity-card {
            background: white;
            border-radius: var(--radius-lg);
            padding: 1.5rem;
            box-shadow: var(--shadow);
            margin-bottom: 2rem;
            border: 1px solid var(--gray-100);
        }
        
        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 1.5rem;
        }
        
        .card-title {
            font-size: 1.125rem;
            font-weight: 600;
        }
        
        .card-actions {
            display: flex;
            align-items: center;
            gap: 0.75rem;
        }
        
        .card-button {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            color: var(--gray-700);
            padding: 0.5rem 1rem;
            border-radius: var(--radius);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition-default);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .card-button:hover {
            background: var(--gray-100);
            color: var(--gray-900);
        }
        
        .card-button.primary {
            background: var(--primary);
            border-color: var(--primary);
            color: white;
        }
        
        .card-button.primary:hover {
            background: var(--primary-dark);
        }
        
        .activity-list {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }
        
        .activity-item {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid var(--gray-100);
        }
        
        .activity-item:last-child {
            padding-bottom: 0;
            border-bottom: none;
        }
        
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1rem;
            flex-shrink: 0;
        }
        
        .activity-icon.blue {
            background: var(--primary-50);
            color: var(--primary);
        }
        
        .activity-icon.green {
            background: #ecfdf5;
            color: var(--success);
        }
        
        .activity-icon.orange {
            background: #fff7ed;
            color: var(--warning);
        }
        
        .activity-icon.red {
            background: #fee2e2;
            color: var(--danger);
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-title {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }
        
        .activity-title strong {
            font-weight: 600;
            color: var(--gray-900);
        }
        
        .activity-meta {
            display: flex;
            align-items: center;
            gap: 1rem;
            font-size: 0.75rem;
            color: var(--gray-500);
        }
        
        .activity-time {
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        /* Mobile Responsive */
        .mobile-menu-button {
            display: none;
            background: none;
            border: none;
            color: var(--gray-700);
            width: 40px;
            height: 40px;
            border-radius: var(--radius);
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-default);
        }
        
        .mobile-menu-button:hover {
            background: var(--gray-100);
            color: var(--gray-900);
        }
        
        .mobile-overlay {
            position: fixed;
            inset: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 45;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition-default);
        }
        
        .mobile-overlay.active {
            opacity: 1;
            visibility: visible;
        }
        
        @media (max-width: 1024px) {
            .dashboard-container {
                grid-template-columns: 1fr;
            }
            
            .sidebar {
                transform: translateX(-100%);
                box-shadow: var(--shadow-xl);
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0 !important;
            }
            
            .mobile-menu-button {
                display: flex;
            }
            
            .search-bar {
                width: auto;
                flex: 1;
                margin: 0 1rem;
            }
            
            .top-bar {
                padding: 0 1rem;
            }
            
            .top-bar-actions {
                gap: 0.75rem;
            }
            
            .page-content {
                padding: 1.5rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 640px) {
            .profile-info {
                display: none;
            }
            
            .search-bar {
                max-width: 150px;
            }
        }
        
        /* Dark Mode */
        .dark-mode-toggle {
            background: none;
            border: none;
            color: var(--gray-600);
            width: 40px;
            height: 40px;
            border-radius: var(--radius);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition-default);
        }
        
        .dark-mode-toggle:hover {
            background: var(--gray-100);
            color: var(--gray-900);
        }
        
        /* Utility Classes */
        .flex {
            display: flex;
        }
        
        .items-center {
            align-items: center;
        }
        
        .justify-between {
            justify-content: space-between;
        }
        
        .gap-2 {
            gap: 0.5rem;
        }
        
        .gap-3 {
            gap: 0.75rem;
        }
        
        .gap-4 {
            gap: 1rem;
        }
        
        .text-sm {
            font-size: 0.875rem;
        }
        
        .font-medium {
            font-weight: 500;
        }
        
        .text-gray-400 {
            color: var(--gray-400);
        }
        
        .text-gray-700 {
            color: var(--gray-700);
        }
        
        .w-full {
            width: 100%;
        }
    </style>
</head>
<body>
    <div class="dashboard-container" id="dashboardContainer">
        <!-- Mobile Overlay -->
        <div class="mobile-overlay" id="mobileOverlay"></div>
        
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="logo-icon">S</div>
                    <div class="logo-text">Sistema de Pagos</div>
                </div>
                <button class="toggle-sidebar" id="toggleSidebar">
                    <i class="fas fa-chevron-left"></i>
                </button>
            </div>
            
            <div class="sidebar-nav">
                <div class="nav-section">
                    <div class="nav-section-title">Principal</div>
                    
                    <a href="<?php echo e(route('dashboard')); ?>" class="nav-item <?php echo e(request()->routeIs('dashboard') ? 'active' : ''); ?>">
                        <div class="nav-icon">
                            <i class="fas fa-home"></i>
                        </div>
                        <span class="nav-text">Panel Principal</span>
                        <div class="nav-tooltip">Panel Principal</div>
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Gestión</div>
                    
                    <a href="<?php echo e(route('clients.index')); ?>" class="nav-item <?php echo e(request()->routeIs('clients.*') ? 'active' : ''); ?>">
                        <div class="nav-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <span class="nav-text">Clientes</span>
                        <div class="nav-badge">8</div>
                        <div class="nav-tooltip">Clientes</div>
                    </a>

                    <a href="<?php echo e(route('contracts.index')); ?>" class="nav-item <?php echo e(request()->routeIs('contracts.*') ? 'active' : ''); ?>">
                        <div class="nav-icon">
                            <i class="fas fa-file-contract"></i>
                        </div>
                        <span class="nav-text">Contratos</span>
                        <div class="nav-tooltip">Contratos</div>
                    </a>

                    <a href="<?php echo e(route('payments.index')); ?>" class="nav-item <?php echo e(request()->routeIs('payments.*') ? 'active' : ''); ?>">
                        <div class="nav-icon">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <span class="nav-text">Pagos</span>
                        <div class="nav-badge">3</div>
                        <div class="nav-tooltip">Pagos</div>
                    </a>
                </div>
                
                <div class="nav-section">
                    <div class="nav-section-title">Configuración</div>
                    
                    <a href="<?php echo e(route('profile.edit')); ?>" class="nav-item <?php echo e(request()->routeIs('profile.*') ? 'active' : ''); ?>">
                        <div class="nav-icon">
                            <i class="fas fa-user-circle"></i>
                        </div>
                        <span class="nav-text">Mi Perfil</span>
                        <div class="nav-tooltip">Mi Perfil</div>
                    </a>
                    
                    <a href="#" class="nav-item">
                        <div class="nav-icon">
                            <i class="fas fa-cog"></i>
                        </div>
                        <span class="nav-text">Configuración</span>
                        <div class="nav-tooltip">Configuración</div>
                    </a>
                </div>
            </div>
            
            <div class="sidebar-footer">
                <div class="sidebar-footer-content">
                    <div class="sidebar-footer-title">¿Necesitas ayuda?</div>
                    <div class="sidebar-footer-text">Accede a nuestros recursos de soporte</div>
                    <button class="sidebar-footer-button">
                        <i class="fas fa-question-circle"></i>
                        <span>Centro de Ayuda</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Contenido Principal -->
        <main class="main-content">
            <div class="top-bar">
                <button class="mobile-menu-button" id="mobileMenuButton">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="search-bar">
                    <input type="text" class="search-input" placeholder="Buscar...">
                    <i class="fas fa-search search-icon"></i>
                </div>
                
                <div class="top-bar-actions">
                    <button class="dark-mode-toggle" id="darkModeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    
                    <button class="action-button">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge">3</span>
                    </button>
                    
                    <div class="profile-menu" id="profileMenu">
                        <div class="profile-button">
                            <div class="profile-avatar">
                                <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                            </div>
                            <div class="profile-info">
                                <div class="profile-name"><?php echo e(Auth::user()->name); ?></div>
                                <div class="profile-role">Administrador</div>
                            </div>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                        
                        <div class="profile-dropdown">
                            <div class="dropdown-header">
                                <div class="dropdown-user"><?php echo e(Auth::user()->name); ?></div>
                                <div class="dropdown-email"><?php echo e(Auth::user()->email); ?></div>
                            </div>
                            
                            <a href="<?php echo e(route('profile.edit')); ?>" class="dropdown-item">
                                <div class="dropdown-icon">
                                    <i class="fas fa-user"></i>
                                </div>
                                <span>Mi Perfil</span>
                            </a>
                            
                            <a href="#" class="dropdown-item">
                                <div class="dropdown-icon">
                                    <i class="fas fa-cog"></i>
                                </div>
                                <span>Configuración</span>
                            </a>
                            
                            <div class="dropdown-divider"></div>
                            
                            <form method="POST" action="<?php echo e(route('logout')); ?>" class="w-full">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="dropdown-item danger w-full">
                                    <div class="dropdown-icon">
                                        <i class="fas fa-sign-out-alt"></i>
                                    </div>
                                    <span>Cerrar Sesión</span>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contenido de la página -->
            <?php echo $__env->yieldContent('content'); ?>
        </main>
    </div>
</div>

    <script>
        // Toggle sidebar
        const dashboardContainer = document.getElementById('dashboardContainer');
        const sidebar = document.getElementById('sidebar');
        const mobileOverlay = document.getElementById('mobileOverlay');
        const toggleSidebar = document.getElementById('toggleSidebar');
        const mobileMenuButton = document.getElementById('mobileMenuButton');
        
        toggleSidebar.addEventListener('click', () => {
            dashboardContainer.classList.toggle('collapsed');
        });
        
        mobileMenuButton.addEventListener('click', () => {
            sidebar.classList.add('active');
            mobileOverlay.classList.add('active');
        });
        
        mobileOverlay.addEventListener('click', () => {
            sidebar.classList.remove('active');
            mobileOverlay.classList.remove('active');
        });
        
        // Profile dropdown
        const profileMenu = document.getElementById('profileMenu');
        
        profileMenu.addEventListener('click', () => {
            profileMenu.classList.toggle('active');
        });
        
        document.addEventListener('click', (e) => {
            if (!profileMenu.contains(e.target)) {
                profileMenu.classList.remove('active');
            }
        });
        
        // Dark mode toggle (placeholder - would need additional CSS)
        const darkModeToggle = document.getElementById('darkModeToggle');
        
        darkModeToggle.addEventListener('click', () => {
            document.body.classList.toggle('dark-mode');
            
            if (document.body.classList.contains('dark-mode')) {
                darkModeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                darkModeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });
    </script>
</body>
</html><?php /**PATH C:\xampp\htdocs\pagos\resources\views/layouts/dashboard.blade.php ENDPATH**/ ?>