<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contrato Firmado - {{ config('app.name') }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <style>
        .glass-effect {
            background: rgba(255, 255, 255, 0.25);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.18);
        }
        
        .morphism-button {
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .morphism-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .signature-display {
            background: #ffffff;
            border: 2px solid #e2e8f0;
            border-radius: 0.5rem;
            padding: 1rem;
            min-height: 150px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .signature-image {
            max-width: 100%;
            max-height: 120px;
            border: 1px solid #cbd5e0;
            border-radius: 0.25rem;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-out;
        }
        
        .animate-slide-up {
            animation: slideUp 0.8s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes slideUp {
            from { 
                opacity: 0;
                transform: translateY(30px);
            }
            to { 
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="gradient-bg">
    <div class="min-h-screen flex flex-col">
        <!-- Header -->
        <header class="glass-effect border-b border-white border-opacity-20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
                <div class="flex justify-between items-center">
                    <div class="flex items-center space-x-3">
                        <div class="bg-white bg-opacity-20 p-2 rounded-lg">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                <line x1="16" y1="2" x2="16" y2="6"></line>
                                <line x1="8" y1="2" x2="8" y2="6"></line>
                                <line x1="3" y1="10" x2="21" y2="10"></line>
                            </svg>
                        </div>
                        <div>
                            <h1 class="text-white text-lg font-semibold">{{ config('app.name') }}</h1>
                            <div class="flex items-center space-x-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-white text-opacity-70" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                                    <line x1="16" y1="2" x2="16" y2="6"></line>
                                    <line x1="8" y1="2" x2="8" y2="6"></line>
                                    <line x1="3" y1="10" x2="21" y2="10"></line>
                                </svg>
                                <p class="text-white text-opacity-90 text-sm sm:text-base">
                                    Contrato #{{ $contract->id }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <a href="{{ route('public.search.results', ['cedula' => request('cedula')]) }}" class="glass-effect bg-white bg-opacity-10 hover:bg-opacity-20 text-white font-medium py-2 px-4 rounded-lg transition duration-300 flex items-center justify-center shadow-md">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path d="M19 12H5"></path>
                            <path d="M12 19l-7-7 7-7"></path>
                        </svg>
                        Volver
                    </a>
                </div>
            </div>
        </header>

        <main class="flex-grow py-8 sm:py-12">
            <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="glass-effect rounded-xl shadow-glass overflow-hidden animate-fade-in p-8">
                    <div class="text-center mb-6">
                        <div class="bg-green-600 bg-opacity-20 inline-flex p-3 rounded-full mb-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-green-600" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                <polyline points="22,4 12,14.01 9,11.01"></polyline>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-2">Contrato Firmado Digitalmente</h2>
                        <p class="text-gray-600 max-w-md mx-auto">
                            Este contrato ha sido firmado digitalmente. A continuación puedes ver la firma registrada.
                        </p>
                    </div>

                    <!-- Contract Information -->
                    <div class="bg-gray-50 rounded-lg p-4 mb-6 animate-slide-up" style="animation-delay: 0.2s">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Información del Contrato</h3>
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="font-medium text-gray-700">Cliente:</span>
                                <p class="text-gray-900">{{ $contract->client->nombre }}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Cédula:</span>
                                <p class="text-gray-900">{{ $contract->client->cedula }}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Monto Total:</span>
                                <p class="text-gray-900">${{ number_format($contract->monto_total, 2) }}</p>
                            </div>
                            <div>
                                <span class="font-medium text-gray-700">Estado:</span>
                                <p class="text-gray-900 capitalize">{{ $contract->estado }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- Digital Signature Display -->
                    <div class="mb-6 animate-slide-up" style="animation-delay: 0.4s">
                        <h3 class="text-lg font-semibold text-gray-900 mb-3">Firma Digital</h3>
                        <div class="signature-display">
                            @if($contract->firma)
                                <img src="{{ $contract->firma }}" alt="Firma Digital" class="signature-image">
                            @else
                                <p class="text-gray-500">No hay firma disponible</p>
                            @endif
                        </div>
                        <p class="text-sm text-gray-500 text-center mt-2">
                            Firma registrada digitalmente
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-3 justify-center animate-slide-up" style="animation-delay: 0.6s">
                        <a href="{{ route('contracts.pdf', $contract->id) }}?cedula={{ request('cedula') }}" 
                           class="glass-effect bg-blue-600 bg-opacity-90 hover:bg-opacity-100 text-white font-medium py-3 px-6 rounded-lg shadow-sm transition duration-300 flex items-center justify-center"
                           target="_blank">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Descargar PDF
                        </a>
                        <a href="{{ route('public.search.results', ['cedula' => request('cedula')]) }}" 
                           class="glass-effect bg-gray-600 bg-opacity-90 hover:bg-opacity-100 text-white font-medium py-3 px-6 rounded-lg shadow-sm transition duration-300 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M19 12H5"></path>
                                <path d="M12 19l-7-7 7-7"></path>
                            </svg>
                            Volver a Mis Contratos
                        </a>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="glass-effect border-t border-white border-opacity-20 py-4">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <p class="text-center text-white text-opacity-70 text-sm">
                    © {{ date('Y') }} {{ config('app.name') }}. Sistema de gestión de contratos.
                </p>
            </div>
        </footer>
    </div>

    <script>
        // Show success message if redirected with success
        @if(session('success'))
            Swal.fire({
                icon: 'success',
                title: '¡Éxito!',
                text: '{{ session('success') }}',
                background: 'rgba(255, 255, 255, 0.95)',
                backdrop: 'rgba(26, 86, 219, 0.4)',
                confirmButtonColor: '#10b981',
                showClass: {
                    popup: 'animate__animated animate__fadeIn animate__faster'
                },
                hideClass: {
                    popup: 'animate__animated animate__fadeOut animate__faster'
                }
            });
        @endif
    </script>
</body>
</html>
