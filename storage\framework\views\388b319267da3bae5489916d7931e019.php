

<?php $__env->startSection('content'); ?>
<div class="page-content" style="min-height: 100vh; padding-bottom: 2rem;">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title text-white">Panel de Control</h1>
        <p class="page-description text-white/80">Resumen general del sistema de pagos</p>
    </div>

    <!-- Estadísticas Generales -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon blue">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label text-white/70">Total Clientes</div>
                <div class="stat-value text-white"><?php echo e(number_format($estadisticas['totalClientes'])); ?></div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon green">
                    <i class="fas fa-file-contract"></i>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label text-white/70">Total Contratos</div>
                <div class="stat-value text-white"><?php echo e(number_format($estadisticas['totalContratos'])); ?></div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon purple">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label text-white/70">Total Pagos</div>
                <div class="stat-value text-white"><?php echo e(number_format($estadisticas['totalPagos'])); ?></div>
            </div>
        </div>
    </div>

    <!-- Resumen Financiero -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon indigo">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label text-white/70">Monto Total Contratos</div>
                <div class="stat-value text-white">$ <?php echo e(number_format($estadisticas['montoTotalContratos'], 2)); ?></div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon green">
                    <i class="fas fa-dollar-sign"></i>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label text-white/70">Monto Total Pagado</div>
                <div class="stat-value text-white">$ <?php echo e(number_format($estadisticas['montoTotalPagos'], 2)); ?></div>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-header">
                <div class="stat-icon red">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
            </div>
            <div class="stat-content">
                <div class="stat-label text-white/70">Monto Pendiente</div>
                <div class="stat-value text-white">$ <?php echo e(number_format($estadisticas['montoPendiente'], 2)); ?></div>
            </div>
        </div>
    </div>

    <!-- Gráficos y Análisis -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Gráfico de Pagos Mensuales -->
        <div class="stat-card">
            <h3 class="text-lg font-semibold text-white mb-4">
                <i class="fas fa-chart-line mr-2 text-blue-400"></i>
                Pagos Mensuales
            </h3>
            <div class="chart-container" style="height: 300px;">
                <canvas id="pagosMensualesChart"></canvas>
            </div>
        </div>

        <!-- Gráfico de Estado de Contratos -->
        <div class="stat-card">
            <h3 class="text-lg font-semibold text-white mb-4">
                <i class="fas fa-chart-pie mr-2 text-green-400"></i>
                Estado de Contratos
            </h3>
            <div class="chart-container" style="height: 300px;">
                <canvas id="estadoContratosChart"></canvas>
            </div>
        </div>
    </div>

    <!-- Tablas de Datos -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Pagos Recientes -->
        <div class="stat-card">
            <h3 class="text-lg font-semibold text-white mb-4">
                <i class="fas fa-clock mr-2 text-purple-400"></i>
                Pagos Recientes
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white/20">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-white/70 uppercase">Cliente</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-white/70 uppercase">Monto</th>
                            <th class="px-4 py-3 text-center text-xs font-medium text-white/70 uppercase">Fecha</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white/10">
                        <?php $__currentLoopData = $pagosRecientes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pago): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="hover:bg-white/5 transition-colors">
                            <td class="px-4 py-3 text-sm text-white"><?php echo e($pago['cliente']); ?></td>
                            <td class="px-4 py-3 text-sm text-white text-right">$ <?php echo e(number_format($pago['monto'], 2)); ?></td>
                            <td class="px-4 py-3 text-sm text-white/80 text-center"><?php echo e($pago['fecha']); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Contratos Próximos a Vencer -->
        <div class="stat-card">
            <h3 class="text-lg font-semibold text-white mb-4">
                <i class="fas fa-calendar-alt mr-2 text-orange-400"></i>
                Contratos Próximos a Vencer
            </h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white/20">
                    <thead>
                        <tr>
                            <th class="px-4 py-3 text-left text-xs font-medium text-white/70 uppercase">Cliente</th>
                            <th class="px-4 py-3 text-center text-xs font-medium text-white/70 uppercase">Fecha Fin</th>
                            <th class="px-4 py-3 text-right text-xs font-medium text-white/70 uppercase">Días Rest.</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white/10">
                        <?php $__currentLoopData = $contratosProximos; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contrato): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr class="hover:bg-white/5 transition-colors">
                            <td class="px-4 py-3 text-sm text-white"><?php echo e($contrato['cliente']); ?></td>
                            <td class="px-4 py-3 text-sm text-white/80 text-center"><?php echo e($contrato['fecha_fin']); ?></td>
                            <td class="px-4 py-3 text-sm text-orange-400 text-right font-medium"><?php echo e($contrato['dias_restantes']); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
console.log('Chart.js cargado:', typeof Chart !== 'undefined');

// Configuración global para Chart.js con tema oscuro
Chart.defaults.color = '#e5e5e5';
Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';
Chart.defaults.backgroundColor = 'rgba(59, 130, 246, 0.1)';

// Gráfico de Pagos Mensuales
const ctxPagos = document.getElementById('pagosMensualesChart').getContext('2d');

// Datos de pagos por mes desde el controller
const pagosPorMes = <?php echo json_encode($pagosPorMes, 15, 512) ?>;
console.log('Datos de pagos por mes:', pagosPorMes);

// Crear array de datos para los 12 meses
const mesesData = [
    pagosPorMes['Enero'] || 0,
    pagosPorMes['Febrero'] || 0,
    pagosPorMes['Marzo'] || 0,
    pagosPorMes['Abril'] || 0,
    pagosPorMes['Mayo'] || 0,
    pagosPorMes['Junio'] || 0,
    pagosPorMes['Julio'] || 0,
    pagosPorMes['Agosto'] || 0,
    pagosPorMes['Septiembre'] || 0,
    pagosPorMes['Octubre'] || 0,
    pagosPorMes['Noviembre'] || 0,
    pagosPorMes['Diciembre'] || 0
];

console.log('Datos procesados:', mesesData);

const pagosMensualesChart = new Chart(ctxPagos, {
    type: 'line',
    data: {
        labels: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
        datasets: [{
            label: 'Pagos Recibidos ($)',
            data: mesesData,
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4,
            pointBackgroundColor: '#3b82f6',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 2,
            pointRadius: 6,
            pointHoverRadius: 8
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                display: true,
                labels: {
                    color: '#e5e5e5',
                    font: {
                        family: 'Inter',
                        size: 12
                    }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                    color: '#a3a3a3',
                    callback: function(value) {
                        return '$' + value.toLocaleString();
                    }
                }
            },
            x: {
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                },
                ticks: {
                    color: '#a3a3a3'
                }
            }
        }
    }
});

// Gráfico de Estado de Contratos
const ctxContratos = document.getElementById('estadoContratosChart').getContext('2d');
const estadoContratosChart = new Chart(ctxContratos, {
    type: 'doughnut',
    data: {
        labels: ['Activos', 'Por Vencer', 'Vencidos', 'Renovados'],
        datasets: [{
            data: [<?php echo e($estadisticas['totalContratos'] * 0.6); ?>, <?php echo e($estadisticas['totalContratos'] * 0.2); ?>, <?php echo e($estadisticas['totalContratos'] * 0.1); ?>, <?php echo e($estadisticas['totalContratos'] * 0.1); ?>],
            backgroundColor: [
                '#10b981',
                '#f59e0b',
                '#ef4444',
                '#3b82f6'
            ],
            borderColor: [
                '#059669',
                '#d97706',
                '#dc2626',
                '#2563eb'
            ],
            borderWidth: 2,
            hoverOffset: 10
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
            legend: {
                position: 'bottom',
                labels: {
                    color: '#e5e5e5',
                    font: {
                        family: 'Inter',
                        size: 12
                    },
                    padding: 20,
                    usePointStyle: true,
                    pointStyle: 'circle'
                }
            }
        }
    }
});
</script>
<?php $__env->stopSection(); ?>



<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\pagos\resources\views/dashboard.blade.php ENDPATH**/ ?>