

<?php $__env->startSection('content'); ?>
<div class="page-content">
    <!-- <PERSON> Header -->
    <div class="page-header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="page-title">Clientes</h1>
                <p class="page-description">Gestión de clientes del sistema</p>
            </div>
            <a href="<?php echo e(route('clients.create')); ?>" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>
                Crear Cliente
            </a>
        </div>
    </div>

    <!-- Content Card -->
    <div class="stat-card">
        <div class="p-6">
            <?php if(session('success')): ?>
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle mr-2"></i>
                    <span><?php echo e(session('success')); ?></span>
                </div>
            <?php endif; ?>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white/20">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">Cédula</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">Nombre</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">Teléfono</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">Contratos</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">Acciones</th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white/10">
                        <?php $__empty_1 = true; $__currentLoopData = $clients; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $client): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="hover:bg-white/5 transition-colors">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white"><?php echo e($client->cedula); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white"><?php echo e($client->nombre_completo); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white/80"><?php echo e($client->email ?? 'N/A'); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white/80"><?php echo e($client->telefono ?? 'N/A'); ?></td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        <?php echo e($client->contracts->count()); ?>

                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div class="flex space-x-2">
                                        <a href="<?php echo e(route('clients.show', $client)); ?>" class="btn-action btn-view">
                                            <i class="fas fa-eye"></i>
                                            Ver
                                        </a>
                                        <a href="<?php echo e(route('clients.edit', $client)); ?>" class="btn-action btn-edit">
                                            <i class="fas fa-edit"></i>
                                            Editar
                                        </a>
                                        <form action="<?php echo e(route('clients.destroy', $client)); ?>" method="POST" class="inline">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn-action btn-delete" onclick="return confirm('¿Está seguro de eliminar este cliente?')">
                                                <i class="fas fa-trash"></i>
                                                Eliminar
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="6" class="px-6 py-8 text-center text-white/60">
                                    <div class="flex flex-col items-center">
                                        <i class="fas fa-users text-4xl mb-4 text-white/30"></i>
                                        <p class="text-lg font-medium">No hay clientes registrados</p>
                                        <p class="text-sm">Comienza agregando tu primer cliente</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <div class="mt-6">
                <?php echo e($clients->links()); ?>

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.dashboard', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\pagos\resources\views/clients/index.blade.php ENDPATH**/ ?>