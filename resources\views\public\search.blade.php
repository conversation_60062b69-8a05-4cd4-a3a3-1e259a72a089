<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ config('app.name') }} - Portal Empresarial Ejecutivo de Consultas</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Poppins:wght@300;400;500;600;700;800;900&family=Playfair+Display:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif'],
                        'display': ['Poppins', 'system-ui', 'sans-serif'],
                    },
                    colors: {
                        primary: {
                            50: '#f8fafc',
                            100: '#f1f5f9',
                            200: '#e2e8f0',
                            300: '#cbd5e1',
                            400: '#94a3b8',
                            500: '#64748b',
                            600: '#475569',
                            700: '#334155',
                            800: '#1e293b',
                            900: '#0f172a',
                        },
                        accent: {
                            50: '#fffbeb',
                            100: '#fef3c7',
                            200: '#fde68a',
                            300: '#fcd34d',
                            400: '#fbbf24',
                            500: '#f59e0b',
                            600: '#d97706',
                            700: '#b45309',
                            800: '#92400e',
                            900: '#78350f',
                        },
                        luxury: {
                            50: '#fefce8',
                            100: '#fef9c3',
                            200: '#fef08a',
                            300: '#fde047',
                            400: '#facc15',
                            500: '#eab308',
                            600: '#ca8a04',
                            700: '#a16207',
                            800: '#854d0e',
                            900: '#713f12',
                        }
                    },
                    animation: {
                        'gradient': 'gradient 8s linear infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 3s infinite',
                    }
                }
            }
        }
    </script>
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 25%, #16213e 50%, #0f3460 75%, #533483 100%);
            --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 50%, #4facfe 100%);
            --luxury-gradient: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
            --platinum-gradient: linear-gradient(135deg, #e5e7eb 0%, #f3f4f6 50%, #ffffff 100%);
            --glass-bg: rgba(255, 255, 255, 0.02);
            --glass-border: rgba(255, 255, 255, 0.06);
            --glass-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            --card-bg: rgba(255, 255, 255, 0.04);
            --card-border: rgba(255, 255, 255, 0.08);
            --shadow-glow: 0 0 80px rgba(103, 126, 234, 0.2);
            --shadow-card: 0 30px 60px -12px rgba(0, 0, 0, 0.4);
            --shadow-input: 0 15px 35px -5px rgba(0, 0, 0, 0.2);
            --morphism-bg: rgba(255, 255, 255, 0.03);
            --morphism-border: rgba(255, 255, 255, 0.08);
            --morphism-shadow: 0 20px 40px 0 rgba(0, 0, 0, 0.3);
            --text-primary: #ffffff;
            --text-secondary: rgba(255, 255, 255, 0.85);
            --text-muted: rgba(255, 255, 255, 0.65);
            --text-accent: #ffd700;
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', system-ui, sans-serif;
            background: var(--primary-gradient);
            background-size: 400% 400%;
            animation: gradient 20s ease infinite;
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(ellipse at 20% 80%, rgba(102, 126, 234, 0.12) 0%, transparent 60%),
                radial-gradient(ellipse at 80% 20%, rgba(240, 147, 251, 0.08) 0%, transparent 60%),
                radial-gradient(ellipse at 40% 40%, rgba(79, 172, 254, 0.06) 0%, transparent 60%),
                radial-gradient(ellipse at 60% 70%, rgba(255, 215, 0, 0.04) 0%, transparent 50%);
            z-index: -1;
        }

        body::after {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.015'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
            z-index: -1;
        }

        @keyframes gradient {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-30px) rotate(5deg); }
        }

        @keyframes pulse-glow {
            0%, 100% { box-shadow: var(--shadow-glow); }
            50% { box-shadow: 0 0 60px rgba(59, 130, 246, 0.5); }
        }

        .animated-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -2;
            background: var(--primary-gradient);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }

        .floating-shapes {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .shape {
            position: absolute;
            opacity: 0.1;
            animation: float 20s infinite ease-in-out;
        }

        .shape:nth-child(1) {
            top: 10%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 50%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            top: 20%;
            right: 10%;
            width: 120px;
            height: 120px;
            background: white;
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
            animation-delay: 5s;
        }

        .shape:nth-child(3) {
            bottom: 20%;
            left: 15%;
            width: 60px;
            height: 60px;
            background: white;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            animation-delay: 10s;
        }

        .shape:nth-child(4) {
            bottom: 30%;
            right: 20%;
            width: 100px;
            height: 100px;
            background: white;
            border-radius: 20px;
            animation-delay: 15s;
        }

        .glass-card {
            background: var(--card-bg);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--card-border);
            box-shadow: var(--morphism-shadow);
            border-radius: 28px;
            position: relative;
            overflow: hidden;
        }

        .glass-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }

        .glass-card::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 1px;
            height: 100%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.2), transparent);
        }

        .hero-title {
            font-family: 'Playfair Display', serif;
            font-weight: 700;
            font-size: clamp(3rem, 7vw, 5rem);
            line-height: 1.1;
            letter-spacing: -0.02em;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 50%, #ffd700 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            text-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
            margin-bottom: 1.5rem;
        }

        .hero-subtitle {
            font-size: clamp(1.2rem, 2.8vw, 1.6rem);
            line-height: 1.8;
            color: var(--text-secondary);
            font-weight: 400;
            margin-bottom: 2rem;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 215, 0, 0.1);
            border: 1px solid rgba(255, 215, 0, 0.2);
            border-radius: 50px;
            color: var(--text-accent);
            font-size: 0.9rem;
            font-weight: 600;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            margin-bottom: 2rem;
        }

        .form-input {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 1.1rem;
            font-weight: 500;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .form-input:focus {
            transform: translateY(-2px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37), 0 0 0 4px rgba(79, 172, 254, 0.2);
            background: rgba(255, 255, 255, 0.95);
            border-color: rgba(79, 172, 254, 0.5);
        }

        .btn-primary {
            background: var(--secondary-gradient);
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            font-weight: 600;
            font-size: 1.1rem;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            transition: left 0.6s;
        }

        .btn-primary::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 40px -12px rgba(79, 172, 254, 0.6);
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }

        .feature-card {
            background: var(--morphism-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--morphism-border);
            box-shadow: var(--morphism-shadow);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }

        .feature-card:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 20px 40px -12px rgba(31, 38, 135, 0.4);
            background: rgba(255, 255, 255, 0.15);
            border-color: rgba(255, 255, 255, 0.3);
        }

        .icon-gradient {
            background: var(--accent-gradient);
        }

        .logo-container {
            animation: float 8s ease-in-out infinite;
            filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));
        }

        @media (max-width: 768px) {
            .glass-card {
                margin: 1rem;
                padding: 2rem 1.5rem;
            }
            
            .hero-title {
                font-size: 2.5rem;
            }
            
            .hero-subtitle {
                font-size: 1.1rem;
            }
        }

        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Morphism Glass Effects */
        .morphism-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(30px);
            -webkit-backdrop-filter: blur(30px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px 0 rgba(31, 38, 135, 0.37),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            border-radius: 24px;
            position: relative;
            overflow: hidden;
        }

        .morphism-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
        }

        .morphism-button {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 
                0 8px 32px 0 rgba(31, 38, 135, 0.37),
                inset 0 1px 0 rgba(255, 255, 255, 0.4);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .morphism-button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 
                0 12px 40px 0 rgba(31, 38, 135, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.5);
        }

        /* Floating particles effect */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            animation: float-particle 15s infinite linear;
        }

        @keyframes float-particle {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100vh) rotate(360deg);
                opacity: 0;
            }
        }

        .particle:nth-child(1) { left: 10%; animation-delay: 0s; }
        .particle:nth-child(2) { left: 20%; animation-delay: 2s; }
        .particle:nth-child(3) { left: 30%; animation-delay: 4s; }
        .particle:nth-child(4) { left: 40%; animation-delay: 6s; }
        .particle:nth-child(5) { left: 50%; animation-delay: 8s; }
        .particle:nth-child(6) { left: 60%; animation-delay: 10s; }
        .particle:nth-child(7) { left: 70%; animation-delay: 12s; }
        .particle:nth-child(8) { left: 80%; animation-delay: 14s; }
        .particle:nth-child(9) { left: 90%; animation-delay: 16s; }
    </style>
</head>
<body class="min-h-screen antialiased">
    <!-- Animated Background -->
    <div class="animated-bg"></div>
    
    <!-- Floating Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Floating Particles -->
    <div class="particles">
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
        <div class="particle"></div>
    </div>

    <div class="min-h-screen flex items-center justify-center px-4 py-8 relative">
        <div class="w-full max-w-7xl mx-auto">
            <!-- Header Section -->
            <div class="text-center mb-16">
                <!-- Logo/Icon -->
                <div class="logo-container inline-block mb-8">
                    <div class="w-28 h-28 mx-auto bg-white/8 backdrop-blur-xl rounded-3xl flex items-center justify-center border border-white/25 relative overflow-hidden" style="animation: pulse-glow 3s ease-in-out infinite;">
                        <div class="absolute inset-0 bg-gradient-to-br from-white/10 to-transparent"></div>
                        <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/40 to-transparent"></div>
                        <svg class="w-14 h-14 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                </div>

                <!-- Badge -->
                <div class="hero-badge">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                    </svg>
                    Portal Empresarial Certificado
                </div>

                <!-- Title -->
                <h1 class="hero-title">
                    Sistema Ejecutivo<br>
                    <span class="bg-gradient-to-r from-yellow-200 via-yellow-300 to-yellow-400 bg-clip-text text-transparent">
                        de Consultas
                    </span>
                </h1>

                <!-- Subtitle -->
                <p class="hero-subtitle max-w-4xl mx-auto">
                    Plataforma empresarial de última generación para el acceso seguro y profesional 
                    a su información financiera. Tecnología avanzada al servicio de la excelencia.
                </p>

                <!-- Trust Indicators -->
                <div class="flex flex-wrap items-center justify-center gap-8 text-white/80 text-sm">
                    <div class="flex items-center space-x-3 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm border border-white/10">
                        <div class="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                            </svg>
                        </div>
                        <span class="font-medium">Seguridad Bancaria</span>
                    </div>
                    <div class="flex items-center space-x-3 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm border border-white/10">
                        <div class="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                        <span class="font-medium">Procesamiento Instantáneo</span>
                    </div>
                    <div class="flex items-center space-x-3 bg-white/5 px-4 py-2 rounded-full backdrop-blur-sm border border-white/10">
                        <div class="w-8 h-8 bg-yellow-500/20 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                        <span class="font-medium">Datos Certificados</span>
                    </div>
                </div>
            </div>

            <!-- Main Content -->
            <div class="grid lg:grid-cols-5 gap-12 items-start">
                <!-- Search Form - Main Content -->
                <div class="lg:col-span-3">
                    <div class="glass-card p-10 lg:p-12">
                        <div class="max-w-lg mx-auto">
                            <!-- Form Header -->
                            <div class="text-center mb-12">
                                <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-yellow-400 via-yellow-500 to-yellow-600 rounded-3xl mb-8 relative overflow-hidden shadow-2xl">
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent"></div>
                                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/70 to-transparent"></div>
                                    <svg class="w-12 h-12 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                </div>
                                <h2 class="text-3xl font-bold text-white mb-3 font-display">Portal de Acceso</h2>
                                <p class="text-white/80 text-lg">Sistema empresarial de consultas avanzadas</p>
                                <div class="mt-4 inline-flex items-center px-4 py-2 bg-white/10 rounded-full text-sm text-white/70 backdrop-blur-sm border border-white/20">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                    Conexión SSL Certificada
                                </div>
                            </div>

                            @if(session('error'))
                            <div class="bg-red-50 border-l-4 border-red-400 rounded-xl p-5 mb-8" role="alert">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <div class="w-10 h-10 bg-red-100 rounded-full flex items-center justify-center">
                                            <svg class="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <h3 class="text-sm font-semibold text-red-800">Error en la consulta</h3>
                                        <p class="text-sm text-red-700 mt-1">{{ session('error') }}</p>
                                    </div>
                                </div>
                            </div>
                            @endif

                            <form action="{{ route('public.search') }}" method="GET" class="space-y-8" id="searchForm">
                                <!-- Input Field -->
                                <div class="space-y-4">
                                    <label for="cedula" class="block text-sm font-semibold text-white/90 uppercase tracking-wide">
                                        Número de Identificación
                                    </label>
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                                            <div class="w-8 h-8 bg-white/10 rounded-lg flex items-center justify-center">
                                                <svg class="h-5 w-5 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                                                </svg>
                                            </div>
                                        </div>
                                        <input 
                                            type="text" 
                                            name="cedula" 
                                            id="cedula" 
                                            required
                                            class="form-input w-full pl-20 pr-6 py-6 border-2 border-white/20 rounded-2xl text-white placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-yellow-500/50 focus:border-yellow-500/50 bg-white/5 backdrop-blur-sm text-lg font-medium"
                                            placeholder="Ingrese su número de cédula"
                                            value="{{ old('cedula') }}"
                                            pattern="[0-9]+"
                                            title="Solo se permiten números"
                                            maxlength="15"
                                        >
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center space-x-2 text-xs text-white/60">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <span>Solo números, sin puntos ni espacios</span>
                                        </div>
                                        <div class="text-xs text-white/40">
                                            Ej: 12345678
                                        </div>
                                    </div>
                                </div>

                                <!-- Submit Button -->
                                <button 
                                    type="submit" 
                                    class="btn-primary w-full py-6 px-8 text-gray-900 font-bold rounded-2xl focus:outline-none focus:ring-4 focus:ring-yellow-500/50 disabled:opacity-50 disabled:cursor-not-allowed bg-gradient-to-r from-yellow-400 via-yellow-500 to-yellow-600 hover:from-yellow-500 hover:via-yellow-600 hover:to-yellow-700 shadow-2xl transform transition-all duration-300 hover:scale-105 hover:-translate-y-2 text-lg uppercase tracking-wide"
                                    id="submitBtn"
                                >
                                    <span class="flex items-center justify-center" id="btnContent">
                                        <div class="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center mr-3">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                            </svg>
                                        </div>
                                        Acceder al Sistema
                                    </span>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Features Sidebar -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Security Feature -->
                    <div class="feature-card p-8">
                        <div class="flex items-start space-x-5">
                            <div class="flex-shrink-0">
                                <div class="w-16 h-16 bg-gradient-to-br from-green-400 via-green-500 to-green-600 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent"></div>
                                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/70 to-transparent"></div>
                                    <svg class="w-8 h-8 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-white mb-3 font-display">Seguridad Empresarial</h3>
                                <p class="text-white/80 leading-relaxed text-sm">
                                    Encriptación SSL de grado bancario con certificación ISO 27001. 
                                    Protección multicapa y auditorías de seguridad continuas.
                                </p>
                                <div class="mt-3 flex items-center text-xs text-green-300">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                                    Certificado Activo
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Speed Feature -->
                    <div class="feature-card p-8">
                        <div class="flex items-start space-x-5">
                            <div class="flex-shrink-0">
                                <div class="w-16 h-16 bg-gradient-to-br from-blue-400 via-blue-500 to-cyan-500 rounded-3xl flex items-center justify-center shadow-2xl relative overflow-hidden">
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/30 to-transparent"></div>
                                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/70 to-transparent"></div>
                                    <svg class="w-8 h-8 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-white mb-3 font-display">Procesamiento Ultrarrápido</h3>
                                <p class="text-white/80 leading-relaxed text-sm">
                                    Infraestructura en la nube con CDN global. Consultas procesadas 
                                    en menos de 800ms con disponibilidad del 99.9%.
                                </p>
                                <div class="mt-3 flex items-center text-xs text-blue-300">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full mr-2 animate-pulse"></div>
                                    Tiempo promedio: 0.8s
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Support Feature -->
                    <div class="feature-card p-8">
                        <div class="flex items-start space-x-5">
                            <div class="flex-shrink-0">
                                <div class="w-16 h-16 bg-gradient-to-br from-purple-400 via-purple-500 to-pink-500 rounded-3xl flex items-center justify-center shadow-lg relative overflow-hidden">
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
                                    <svg class="w-7 h-7 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-white mb-3">Soporte Especializado</h3>
                                <p class="text-white/80 leading-relaxed">
                                    Equipo técnico disponible 24/7 para resolver cualquier consulta o inconveniente.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Stats -->
                    <div class="feature-card p-6">
                        <h3 class="text-lg font-bold text-white mb-4 text-center">Estadísticas del Sistema</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-300">99.9%</div>
                                <div class="text-sm text-white/70">Disponibilidad</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-300">< 2s</div>
                                <div class="text-sm text-white/70">Tiempo Respuesta</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-purple-300">24/7</div>
                                <div class="text-sm text-white/70">Disponible</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-orange-300">SSL</div>
                                <div class="text-sm text-white/70">Encriptado</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Footer Info -->
            <div class="mt-20 text-center">
                <div class="glass-card p-8 max-w-4xl mx-auto">
                    <div class="grid md:grid-cols-2 gap-8 items-center">
                        <!-- Help Section -->
                        <div class="text-left">
                            <div class="flex items-center space-x-3 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center relative overflow-hidden">
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
                                    <svg class="w-6 h-6 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <h4 class="text-xl font-bold text-white">¿Necesitas Ayuda?</h4>
                            </div>
                            <p class="text-white/80 mb-6 leading-relaxed">
                                Nuestro equipo de soporte está disponible para asistirte con cualquier consulta o problema técnico que puedas tener.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-3">
                                <a href="mailto:<EMAIL>" class="morphism-button inline-flex items-center px-6 py-3 text-white rounded-xl transition-all duration-200">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    Enviar Email
                                </a>
                                <a href="tel:+573001234567" class="morphism-button inline-flex items-center px-6 py-3 text-white rounded-xl transition-all duration-200">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                                    </svg>
                                    Llamar Ahora
                                </a>
                            </div>
                        </div>

                        <!-- Company Info -->
                        <div class="text-left">
                            <div class="flex items-center space-x-3 mb-4">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center relative overflow-hidden">
                                    <div class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent"></div>
                                    <div class="absolute top-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-white/50 to-transparent"></div>
                                    <svg class="w-6 h-6 text-white relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                </div>
                                <h4 class="text-xl font-bold text-white">{{ config('app.name') }}</h4>
                            </div>
                            <p class="text-white/80 mb-4 leading-relaxed">
                                Plataforma confiable para la gestión y consulta de información de pagos con tecnología de vanguardia.
                            </p>
                            <div class="space-y-2 text-sm text-white/60">
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                    </svg>
                                    <span>Colombia</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span>Disponible 24/7</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitBtn');
            const btnContent = document.getElementById('btnContent');
            
            submitBtn.disabled = true;
            btnContent.innerHTML = `
                <div class="loading-spinner mr-3"></div>
                Consultando...
            `;
        });

        // Input validation
        document.getElementById('cedula').addEventListener('input', function(e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    </script>
</body>
</html>