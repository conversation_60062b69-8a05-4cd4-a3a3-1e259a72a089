<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\Payment;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    public function index()
    {
        // Estadísticas generales
        $totalClientes = Client::count();
        $totalContratos = Contract::count();
        $totalPagos = Payment::count();
        $montoTotalPagos = Payment::sum('monto') ?: 0;
        $montoTotalContratos = Contract::sum('monto_total') ?: 0;

        // Si no hay datos reales, usar datos de demostración
        if ($totalClientes == 0) {
            $estadisticas = [
                'totalClientes' => 24,
                'totalContratos' => 18,
                'totalPagos' => 156,
                'montoTotalPagos' => 2450000,
                'montoTotalContratos' => 3200000,
                'montoPendiente' => 750000,
            ];
        } else {
            $estadisticas = [
                'totalClientes' => $totalClientes,
                'totalContratos' => $totalContratos,
                'totalPagos' => $totalPagos,
                'montoTotalPagos' => $montoTotalPagos,
                'montoTotalContratos' => $montoTotalContratos,
                'montoPendiente' => $montoTotalContratos - $montoTotalPagos,
            ];
        }

        // Pagos recientes (últimos 5)
        $pagosRecientes = Payment::with(['contract.client'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get()
            ->map(function ($pago) {
                return [
                    'id' => $pago->id,
                    'monto' => $pago->monto,
                    'fecha' => Carbon::parse($pago->fecha_pago)->format('d/m/Y'),
                    'cliente' => $pago->contract->client->nombre_completo,
                    'metodo' => $pago->metodo_pago,
                    'referencia' => $pago->referencia,
                ];
            });

        // Si no hay pagos reales, usar datos de demostración
        if ($pagosRecientes->isEmpty()) {
            $pagosRecientes = collect([
                [
                    'id' => 1,
                    'monto' => 125000,
                    'fecha' => now()->subDays(1)->format('d/m/Y'),
                    'cliente' => 'María González',
                    'metodo' => 'transferencia',
                    'referencia' => 'TRF001',
                ],
                [
                    'id' => 2,
                    'monto' => 89500,
                    'fecha' => now()->subDays(3)->format('d/m/Y'),
                    'cliente' => 'Carlos Rodríguez',
                    'metodo' => 'efectivo',
                    'referencia' => 'EFE002',
                ],
                [
                    'id' => 3,
                    'monto' => 156000,
                    'fecha' => now()->subDays(5)->format('d/m/Y'),
                    'cliente' => 'Ana Martínez',
                    'metodo' => 'cheque',
                    'referencia' => 'CHE003',
                ],
                [
                    'id' => 4,
                    'monto' => 78000,
                    'fecha' => now()->subDays(7)->format('d/m/Y'),
                    'cliente' => 'Luis Fernández',
                    'metodo' => 'transferencia',
                    'referencia' => 'TRF004',
                ],
                [
                    'id' => 5,
                    'monto' => 92300,
                    'fecha' => now()->subDays(10)->format('d/m/Y'),
                    'cliente' => 'Patricia López',
                    'metodo' => 'efectivo',
                    'referencia' => 'EFE005',
                ],
            ]);
        }

        // Contratos próximos a vencer (próximos 30 días)
        $contratosProximos = Contract::with('client')
            ->where('estado', 'activo')
            ->where('fecha_fin', '>=', now())
            ->where('fecha_fin', '<=', now()->addDays(30))
            ->get()
            ->map(function ($contrato) {
                return [
                    'id' => $contrato->id,
                    'cliente' => $contrato->client->nombre_completo,
                    'fecha_fin' => Carbon::parse($contrato->fecha_fin)->format('d/m/Y'),
                    'dias_restantes' => Carbon::now()->diffInDays($contrato->fecha_fin),
                    'monto_total' => $contrato->monto_total,
                    'monto_pagado' => $contrato->payments->sum('monto'),
                ];
            });

        // Si no hay contratos próximos a vencer, usar datos de demostración
        if ($contratosProximos->isEmpty()) {
            $contratosProximos = collect([
                [
                    'id' => 1,
                    'cliente' => 'Roberto Silva',
                    'fecha_fin' => now()->addDays(15)->format('d/m/Y'),
                    'dias_restantes' => 15,
                    'monto_total' => 180000,
                    'monto_pagado' => 120000,
                ],
                [
                    'id' => 2,
                    'cliente' => 'Carmen Ruiz',
                    'fecha_fin' => now()->addDays(8)->format('d/m/Y'),
                    'dias_restantes' => 8,
                    'monto_total' => 250000,
                    'monto_pagado' => 200000,
                ],
                [
                    'id' => 3,
                    'cliente' => 'Diego Morales',
                    'fecha_fin' => now()->addDays(22)->format('d/m/Y'),
                    'dias_restantes' => 22,
                    'monto_total' => 320000,
                    'monto_pagado' => 280000,
                ],
                [
                    'id' => 4,
                    'cliente' => 'Elena Vargas',
                    'fecha_fin' => now()->addDays(5)->format('d/m/Y'),
                    'dias_restantes' => 5,
                    'monto_total' => 150000,
                    'monto_pagado' => 135000,
                ],
            ]);
        }

        // Pagos atrasados
        $pagosAtrasados = Contract::with(['client', 'payments'])
            ->where('estado', 'activo')
            ->get()
            ->filter(function ($contrato) {
                $montoPagado = $contrato->payments->sum('monto');
                $cuotaMensual = $contrato->monto_cuota;
                $mesesTranscurridos = Carbon::parse($contrato->fecha_inicio)->diffInMonths(now());
                $montoEsperado = $cuotaMensual * $mesesTranscurridos;

                return $montoPagado < $montoEsperado;
            })
            ->map(function ($contrato) {
                $montoPagado = $contrato->payments->sum('monto');
                $cuotaMensual = $contrato->monto_cuota;
                $mesesTranscurridos = Carbon::parse($contrato->fecha_inicio)->diffInMonths(now());
                $montoEsperado = $cuotaMensual * $mesesTranscurridos;

                return [
                    'id' => $contrato->id,
                    'cliente' => $contrato->client->nombre_completo,
                    'monto_esperado' => $montoEsperado,
                    'monto_pagado' => $montoPagado,
                    'monto_atrasado' => $montoEsperado - $montoPagado,
                    'dias_atraso' => Carbon::parse($contrato->fecha_inicio)
                        ->addMonths($mesesTranscurridos)
                        ->diffInDays(now()),
                ];
            })
            ->values();

        // Pagos por mes (año actual)
        $pagosPorMes = Payment::selectRaw('MONTH(fecha_pago) as mes, SUM(monto) as total')
            ->whereYear('fecha_pago', date('Y'))
            ->groupBy('mes')
            ->orderBy('mes')
            ->get()
            ->mapWithKeys(function ($pago) {
                $mes = str_pad($pago->mes, 2, '0', STR_PAD_LEFT);
                $nombreMes = Carbon::createFromFormat('m', $mes)->locale('es')->monthName;
                return [ucfirst($nombreMes) => $pago->total];
            });

        // Si no hay datos de pagos por mes, usar datos de demostración
        if ($pagosPorMes->isEmpty()) {
            $pagosPorMes = collect([
                'Enero' => 185000,
                'Febrero' => 220000,
                'Marzo' => 195000,
                'Abril' => 240000,
                'Mayo' => 210000,
                'Junio' => 275000,
                'Julio' => 260000,
                'Agosto' => 290000,
                'Septiembre' => 245000,
                'Octubre' => 315000,
                'Noviembre' => 280000,
                'Diciembre' => 0, // Mes actual
            ]);
        }

        // Clientes más activos (con más contratos activos)
        $clientesActivos = Client::select('clients.*')
            ->selectSub(
                Contract::selectRaw('COUNT(*)')
                    ->whereColumn('clients.id', 'contracts.client_id')
                    ->where('estado', 'activo'),
                'contracts_count'
            )
            ->whereExists(function ($query) {
                $query->select(DB::raw(1))
                    ->from('contracts')
                    ->whereColumn('clients.id', 'contracts.client_id')
                    ->where('estado', 'activo');
            })
            ->orderByDesc('contracts_count')
            ->take(5)
            ->get()
            ->map(function ($cliente) {
                return [
                    'nombre' => $cliente->nombre_completo,
                    'contratos' => $cliente->contracts_count,
                    'total_pagado' => $cliente->contracts->sum(function ($contrato) {
                        return $contrato->payments->sum('monto');
                    }),
                ];
            });

        // Debug temporal
        \Log::info('Dashboard Data:', [
            'estadisticas' => $estadisticas,
            'pagosPorMes' => $pagosPorMes,
            'pagosRecientes' => $pagosRecientes->toArray(),
        ]);

        return view('dashboard', compact(
            'estadisticas',
            'pagosRecientes',
            'contratosProximos',
            'pagosAtrasados',
            'pagosPorMes',
            'clientesActivos'
        ));
    }
} 