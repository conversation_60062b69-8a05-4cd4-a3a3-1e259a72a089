<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Detalle del Cliente') }}
            </h2>
            <div class="flex space-x-4">
                <a href="{{ route('clients.edit', $client) }}" class="bg-yellow-600 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                    Editar Cliente
                </a>
                <a href="{{ route('contracts.create') }}?client_id={{ $client->id }}" class="bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                    Nuevo Contrato
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 text-gray-900">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Información Personal</h3>
                            <dl class="grid grid-cols-1 gap-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Cédula</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $client->cedula }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Nombre Completo</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $client->nombre_completo }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $client->email ?? 'No especificado' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Teléfono</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $client->telefono ?? 'No especificado' }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Dirección</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $client->direccion ?? 'No especificada' }}</dd>
                                </div>
                            </dl>
                        </div>

                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Resumen de Contratos</h3>
                            <dl class="grid grid-cols-1 gap-4">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Total de Contratos</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $client->contracts->count() }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Contratos Activos</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $client->contracts->where('estado', 'activo')->count() }}</dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500">Contratos Completados</dt>
                                    <dd class="mt-1 text-sm text-gray-900">{{ $client->contracts->where('estado', 'completado')->count() }}</dd>
                                </div>
                            </dl>
                        </div>
                    </div>

                    <div class="mt-8">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Contratos</h3>
                        @forelse($client->contracts as $contract)
                            <div class="border rounded-lg p-4 mb-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="text-lg font-semibold">Contrato #{{ $contract->id }}</h4>
                                        <p class="text-sm text-gray-500">Fecha de inicio: {{ $contract->fecha_inicio->format('d/m/Y') }}</p>
                                    </div>
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {{ $contract->estado === 'completado' ? 'bg-green-100 text-green-800' : 
                                           ($contract->estado === 'activo' ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800') }}">
                                        {{ ucfirst($contract->estado) }}
                                    </span>
                                </div>

                                <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div>
                                        <p class="text-sm text-gray-500">Monto Total</p>
                                        <p class="text-sm font-medium">${{ number_format($contract->monto_total, 2) }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">Cuotas Pagadas</p>
                                        <p class="text-sm font-medium">{{ $contract->cuotas_pagadas }} de {{ $contract->numero_cuotas }}</p>
                                    </div>
                                    <div>
                                        <p class="text-sm text-gray-500">Monto por Cuota</p>
                                        <p class="text-sm font-medium">${{ number_format($contract->monto_cuota, 2) }}</p>
                                    </div>
                                </div>

                                <div class="mt-4 flex justify-end space-x-3">
                                    <a href="{{ route('contracts.show', $contract) }}" class="text-indigo-600 hover:text-indigo-900 text-sm font-medium">Ver Detalles</a>
                                    @if($contract->estado === 'activo')
                                        <a href="{{ route('payments.create') }}?contract_id={{ $contract->id }}" class="text-green-600 hover:text-green-900 text-sm font-medium">Registrar Pago</a>
                                    @endif
                                </div>
                            </div>
                        @empty
                            <p class="text-gray-500 italic">No hay contratos registrados para este cliente.</p>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout> 