// Dashboard Interactive Features
document.addEventListener('DOMContentLoaded', function() {
    
    // Smooth scroll animations for stat cards
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe all stat cards
    document.querySelectorAll('.stat-card, .chart-card, .table-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(card);
    });

    // Counter animation for stat numbers
    function animateCounter(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;
        
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            // Format number based on size
            let displayValue;
            if (target >= 1000000) {
                displayValue = (current / 1000000).toFixed(1) + 'M';
            } else if (target >= 1000) {
                displayValue = (current / 1000).toFixed(1) + 'K';
            } else {
                displayValue = Math.floor(current).toLocaleString();
            }
            
            element.textContent = element.textContent.includes('$') ? '$' + displayValue : displayValue;
        }, 16);
    }

    // Animate stat numbers when they come into view
    const statNumbers = document.querySelectorAll('.stat-number');
    statNumbers.forEach(stat => {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const text = entry.target.textContent.replace(/[$,]/g, '');
                    const target = parseFloat(text);
                    if (!isNaN(target)) {
                        animateCounter(entry.target, target);
                    }
                    observer.unobserve(entry.target);
                }
            });
        }, { threshold: 0.5 });
        
        observer.observe(stat);
    });

    // Interactive chart period buttons
    document.querySelectorAll('.chart-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const parent = this.closest('.chart-header');
            parent.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            // Add loading effect
            const chartContainer = parent.nextElementSibling;
            chartContainer.classList.add('loading');
            
            setTimeout(() => {
                chartContainer.classList.remove('loading');
            }, 1000);
        });
    });

    // Tooltip functionality for metric cards
    document.querySelectorAll('.metric').forEach(metric => {
        metric.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.2)';
        });
        
        metric.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = 'none';
        });
    });

    // Table row hover effects
    document.querySelectorAll('.data-table tr').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(59, 130, 246, 0.08)';
        });
        
        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });

    // Action button interactions
    document.querySelectorAll('.action-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
            
            // Show confirmation or action
            const action = this.textContent.trim();
            console.log(`Acción: ${action} ejecutada`);
        });
    });

    // Refresh data functionality
    function refreshDashboard() {
        // Add loading states to all cards
        document.querySelectorAll('.stat-card, .chart-card, .table-card').forEach(card => {
            card.classList.add('loading');
        });

        // Simulate data refresh
        setTimeout(() => {
            document.querySelectorAll('.stat-card, .chart-card, .table-card').forEach(card => {
                card.classList.remove('loading');
            });
            
            // Show success message
            showNotification('Dashboard actualizado correctamente', 'success');
        }, 2000);
    }

    // Notification system
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                <span>${message}</span>
            </div>
            <button class="notification-close">
                <i class="fas fa-times"></i>
            </button>
        `;
        
        // Add notification styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? 'var(--success-color)' : 'var(--info-color)'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 1rem;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Close functionality
        notification.querySelector('.notification-close').addEventListener('click', () => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        });
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (document.body.contains(notification)) {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    document.body.removeChild(notification);
                }, 300);
            }
        }, 5000);
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + R for refresh
        if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
            e.preventDefault();
            refreshDashboard();
        }
        
        // Escape to close any open modals or notifications
        if (e.key === 'Escape') {
            document.querySelectorAll('.notification').forEach(notification => {
                notification.querySelector('.notification-close').click();
            });
        }
    });

    // Auto-refresh every 5 minutes
    setInterval(() => {
        refreshDashboard();
    }, 300000); // 5 minutes

    console.log('Dashboard interactivity loaded successfully');
});

// Export functions for external use
window.dashboardUtils = {
    refreshDashboard: function() {
        // Trigger refresh
        document.dispatchEvent(new CustomEvent('dashboard:refresh'));
    },
    
    showNotification: function(message, type = 'info') {
        // Use the internal notification function
        showNotification(message, type);
    }
};
